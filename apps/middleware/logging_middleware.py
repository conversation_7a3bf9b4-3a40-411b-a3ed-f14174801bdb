#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:55
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : logging_middleware.py
# @Update  : 2025/8/7 12:55 日志中间件

import logging
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from contexts import get_request_context


class RequestContextLogFilter(logging.Filter):
    """请求上下文日志过滤器
    
    自动在日志记录中添加请求上下文信息
    """

    def filter(self, record):
        """过滤日志记录，添加请求上下文信息"""
        # 获取请求上下文
        context = get_request_context()

        if context:
            # 添加请求ID
            record.request_id = context.request_id
            record.user_id = context.user_id or "anonymous"
            record.ip_address = context.ip_address or "unknown"
            record.method = context.method or ""
            record.path = context.path or ""

            # 添加额外信息
            if context.extra:
                for key, value in context.extra.items():
                    setattr(record, f"ctx_{key}", value)
        else:
            # 如果没有上下文，设置默认值
            record.request_id = "no-request"
            record.user_id = "system"
            record.ip_address = "system"
            record.method = ""
            record.path = ""

        return True


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件
    
    功能：
    1. 配置日志格式以包含请求ID
    2. 记录详细的请求和响应信息
    3. 性能监控
    """

    def __init__(
        self,
        app,
        log_request_body: bool = False,
        log_response_body: bool = False,
        max_body_size: int = 1024,
        exclude_paths: list = None
    ):
        super().__init__(app)
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body
        self.max_body_size = max_body_size
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/favicon.ico"]

        # 安装日志过滤器
        self._install_log_filter()

    def _install_log_filter(self):
        """安装日志过滤器到所有日志器"""
        # 获取根日志器
        root_logger = logging.getLogger()

        # 添加过滤器到所有处理器
        for handler in root_logger.handlers:
            handler.addFilter(RequestContextLogFilter())

        # 也添加到应用特定的日志器
        app_logger = logging.getLogger("echonote")
        for handler in app_logger.handlers:
            handler.addFilter(RequestContextLogFilter())

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 检查是否需要跳过日志记录
        if self._should_exclude_path(request.url.path):
            return await call_next(request)

        # 记录请求体（如果启用）
        request_body = None
        if self.log_request_body:
            request_body = await self._get_request_body(request)

        # 处理请求
        response = await call_next(request)

        # 记录响应体（如果启用）
        response_body = None
        if self.log_response_body:
            response_body = await self._get_response_body(response)

        # 记录详细日志
        self._log_request_response(request, response, request_body, response_body)

        return response

    def _should_exclude_path(self, path: str) -> bool:
        """检查路径是否应该被排除"""
        return path in self.exclude_paths

    async def _get_request_body(self, request: Request) -> str:
        """获取请求体"""
        try:
            body = await request.body()
            if len(body) > self.max_body_size:
                return f"<body too large: {len(body)} bytes>"
            return body.decode("utf-8", errors="replace")
        except Exception as e:
            return f"<error reading body: {str(e)}>"

    async def _get_response_body(self, response: Response) -> str:
        """获取响应体"""
        try:
            if hasattr(response, 'body'):
                body = response.body
                if len(body) > self.max_body_size:
                    return f"<body too large: {len(body)} bytes>"
                return body.decode("utf-8", errors="replace")
            return "<no body>"
        except Exception as e:
            return f"<error reading body: {str(e)}>"

    def _log_request_response(
        self,
        request: Request,
        response: Response,
        request_body: str = None,
        response_body: str = None
    ):
        """记录请求和响应详细信息"""
        from configs.logging_config import get_api_logger

        logger = get_api_logger()

        # 构建日志信息
        log_data = {
            "method":       request.method,
            "url":          str(request.url),
            "status_code":  response.status_code,
            "headers":      dict(request.headers),
            "query_params": dict(request.query_params)
        }

        if request_body:
            log_data["request_body"] = request_body

        if response_body:
            log_data["response_body"] = response_body

        # 根据状态码选择日志级别
        if response.status_code >= 500:
            logger.error("HTTP 5xx Error", extra=log_data)
        elif response.status_code >= 400:
            logger.warning("HTTP 4xx Error", extra=log_data)
        else:
            logger.info("HTTP Request", extra=log_data)


def setup_request_logging():
    """设置请求日志记录
    
    注意：现在日志格式由 configs.logging_config 模块统一管理，
    这里只需要确保日志过滤器正确安装即可。
    """
    # 获取根日志器
    root_logger = logging.getLogger()

    # 检查是否已经安装了过滤器
    for handler in root_logger.handlers:
        if not any(isinstance(f, RequestContextLogFilter) for f in handler.filters):
            handler.addFilter(RequestContextLogFilter())

    # 也检查应用日志器
    app_logger = logging.getLogger("echonote")
    for handler in app_logger.handlers:
        if not any(isinstance(f, RequestContextLogFilter) for f in handler.filters):
            handler.addFilter(RequestContextLogFilter())


# 自动设置请求日志格式
setup_request_logging()
