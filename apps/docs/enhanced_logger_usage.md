# 增强日志器使用说明

## 概述

EchoNote 项目使用了增强的日志器功能，它会自动为所有 `logger.error` 调用添加 `exc_info=True`，确保错误日志包含完整的异常堆栈信息。

## 主要特性

1. **自动异常信息**：所有 `logger.error()` 调用自动包含异常堆栈跟踪
2. **兼容性**：支持手动覆盖 `exc_info` 参数
3. **完整支持**：同时支持 `error()` 和 `exception()` 方法
4. **嵌套异常**：正确处理复杂的异常链

## 使用方法

### 基本使用

```python
from configs.logging_config import get_app_logger

logger = get_app_logger()

# 普通信息日志
logger.info("应用启动成功")

# 错误日志 - 自动包含异常堆栈信息
try:
    result = 10 / 0
except Exception as e:
    logger.error(f"计算过程中发生错误: {e}")
    # 注意：不需要手动添加 exc_info=True
```

### 手动控制异常信息

```python
try:
    data = {"key": "value"}
    result = data["non_existent_key"]
except KeyError as e:
    # 禁用自动添加的异常信息
    logger.error(f"键不存在: {e}", exc_info=False)
    
    # 显式启用异常信息（默认行为）
    logger.error(f"键不存在: {e}", exc_info=True)
```

### 使用 exception 方法

```python
try:
    with open("non_existent_file.txt", "r") as f:
        content = f.read()
except FileNotFoundError as e:
    # exception 方法总是包含异常信息
    logger.exception(f"文件操作失败: {e}")
```

### 处理嵌套异常

```python
try:
    try:
        result = 1 / 0
    except ZeroDivisionError as inner_e:
        raise ValueError("数据处理失败") from inner_e
except Exception as e:
    # 自动显示完整的异常链
    logger.error(f"处理过程中发生错误: {e}")
```

## 技术实现

### 核心类

- `EnhancedLogger`：继承自 `logging.Logger`，重写了 `error` 和 `exception` 方法
- `SafeLogFormatter`：处理缺失的日志字段，确保格式化安全

### 配置方式

```python
# 在 setup_logging 中注册自定义日志器类
logging.setLoggerClass(EnhancedLogger)
```

### 日志格式

增强日志器使用 `request_aware` 格式化器，包含以下字段：

```
%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] [%(user_id)s] [%(ip_address)s] %(method)s %(path)s - %(message)s
```

## 最佳实践

1. **使用 error 方法记录异常**：让系统自动处理异常堆栈信息
2. **在需要时手动控制**：使用 `exc_info=False` 禁用自动添加的异常信息
3. **使用 exception 方法**：专门用于记录异常，总是包含堆栈信息
4. **保持日志简洁**：在异常信息中提供有意义的错误描述

## 示例

查看 `examples/enhanced_logger_example.py` 文件获取完整的使用示例。

## 注意事项

1. 增强日志器只影响通过 `get_logger()` 获取的日志器实例
2. 第三方库的日志器不受影响
3. 可以通过手动指定 `exc_info=False` 来禁用自动添加的异常信息
4. 异常堆栈信息会显著增加日志文件大小，请合理配置日志轮转
