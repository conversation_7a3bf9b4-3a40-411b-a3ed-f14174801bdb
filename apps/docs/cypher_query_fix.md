# Cypher 查询修复说明

## 问题描述

在 Apache AGE 图数据库服务中，出现了 Cypher 查询的列定义不匹配错误：

```
sqlalchemy.exc.ProgrammingError: return row and column definition list do not match
```

## 问题原因

在 `graph_service.py` 文件的 `execute_cypher` 方法中，当 Cypher 查询返回多个列时，SQL 的 `AS` 子句只定义了一个列：

```python
# 错误的查询（多列查询时）
age_query = f"""
SELECT * FROM ag_catalog.cypher('{settings.graph_name}', $$
    MATCH (n) RETURN labels(n)[0] as label, count(n) as count
$$) AS (result agtype);  # 只定义了一个列，但查询返回两个列
"""
```

## 解决方案

根据 Apache AGE 官方文档，修正了查询逻辑：

1. **动态检测列数**：检查 Cypher 查询的 `RETURN` 子句是否包含多个列
2. **动态构建列定义**：根据返回的列数动态构建 SQL 的 `AS` 子句
3. **改进结果解析**：支持单列和多列结果的解析

## 修复内容

### 1. 动态列定义构建

```python
# 检查查询是否返回多个列
if ',' in cypher_query.split('RETURN')[1] if 'RETURN' in cypher_query else False:
    # 多列查询，需要动态构建列定义
    return_part = cypher_query.split('RETURN')[1].strip()
    columns = [col.strip().split(' as ')[1] if ' as ' in col else f"col_{i}" 
              for i, col in enumerate(return_part.split(','))]
    column_defs = ', '.join([f"{col} agtype" for col in columns])
    age_query = f"""
    SELECT * FROM ag_catalog.cypher('{settings.graph_name}', $$
        {cypher_query}
    $$) AS ({column_defs});
    """
else:
    # 单列查询
    age_query = f"""
    SELECT * FROM ag_catalog.cypher('{settings.graph_name}', $$
        {cypher_query}
    $$) AS (result agtype);
    """
```

### 2. 改进结果解析

```python
# 解析AGE结果
results = []
for row in rows:
    if hasattr(row, 'result'):
        # 单列结果
        if row.result:
            try:
                parsed = json.loads(str(row.result))
                results.append(parsed)
            except (json.JSONDecodeError, AttributeError):
                results.append({"result": str(row.result)})
    else:
        # 多列结果
        row_dict = {}
        for i, col in enumerate(row):
            try:
                if col:
                    parsed = json.loads(str(col))
                    row_dict[list(row._fields)[i]] = parsed
                else:
                    row_dict[list(row._fields)[i]] = None
            except (json.JSONDecodeError, AttributeError):
                row_dict[list(row._fields)[i]] = str(col)
        results.append(row_dict)
```

## 技术细节

### Apache AGE 查询语法

根据 Apache AGE 官方文档，当 Cypher 查询返回多个列时，SQL 的 `AS` 子句必须定义所有返回的列：

```sql
-- 单列查询
SELECT * FROM ag_catalog.cypher('graph_name', $$
    MATCH (n) RETURN count(n) as count
$$) AS (result agtype);

-- 多列查询
SELECT * FROM ag_catalog.cypher('graph_name', $$
    MATCH (n) RETURN labels(n)[0] as label, count(n) as count
$$) AS (label agtype, count agtype);
```

### 列名提取逻辑

- 如果列有 `AS` 别名，使用别名作为列名
- 如果没有别名，使用 `col_0`, `col_1` 等作为默认列名
- 所有列都定义为 `agtype` 类型

## 验证结果

修复后的测试显示：

1. ✅ 单列查询成功
2. ✅ 多列查询成功
3. ✅ 图统计信息正常获取
4. ✅ 动态列定义构建正确

## 影响范围

- Apache AGE 图数据库的统计查询功能
- 所有返回多个列的 Cypher 查询
- 图数据库服务的完整功能

## 相关文件

- `apps/services/graph_service.py`：主要的修复文件
- `apps/CHANGELOG.md`：更新日志
- `apps/pyproject.toml`：版本号更新

## 注意事项

1. 这个修复确保了所有 Cypher 查询都能正确执行
2. 支持动态列数检测，提高了代码的灵活性
3. 遵循 Apache AGE 官方文档的最佳实践
4. 向后兼容单列查询
