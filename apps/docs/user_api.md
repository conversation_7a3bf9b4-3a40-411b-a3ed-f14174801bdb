# 用户管理 API 文档

## 概述

用户管理API提供了完整的RESTful风格接口，用于管理系统用户。所有API都遵循REST设计原则，支持标准的HTTP方法和状态码。

## 基础URL

```
/api/v1/users
```

## API 接口列表

### 1. 创建用户

**POST** `/api/v1/users/`

创建新用户账户。

**请求体：**
```json
{
  "username": "string",      // 用户名，3-50字符，必填
  "email": "string",         // 邮箱地址，必填
  "password": "string",      // 密码，至少8位，必须包含大小写字母和数字，必填
  "full_name": "string",     // 全名，可选
  "avatar_url": "string",    // 头像URL，可选
  "bio": "string"            // 个人简介，可选
}
```

**响应：**
- **201 Created** - 用户创建成功
- **400 Bad Request** - 请求数据无效或用户名/邮箱已存在
- **422 Unprocessable Entity** - 数据验证失败

**响应示例：**
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "full_name": "Test User",
  "avatar_url": null,
  "bio": null,
  "is_active": true,
  "is_superuser": false,
  "is_verified": false,
  "created_at": "2025-08-07T10:00:00Z",
  "updated_at": "2025-08-07T10:00:00Z",
  "last_login_at": null
}
```

### 2. 获取用户列表

**GET** `/api/v1/users/`

获取用户列表，支持分页和过滤。

**查询参数：**
- `skip` (int): 跳过的记录数，默认0
- `limit` (int): 返回的记录数，默认10，最大100
- `search` (string): 搜索关键词，在用户名、邮箱、全名中搜索
- `is_active` (boolean): 过滤激活状态
- `is_verified` (boolean): 过滤验证状态

**响应：**
- **200 OK** - 获取成功

**响应示例：**
```json
{
  "users": [
    {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "full_name": "Test User",
      "avatar_url": null,
      "bio": null,
      "is_active": true,
      "is_superuser": false,
      "is_verified": false,
      "created_at": "2025-08-07T10:00:00Z",
      "updated_at": "2025-08-07T10:00:00Z",
      "last_login_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "pages": 1
}
```

### 3. 获取单个用户

**GET** `/api/v1/users/{user_id}`

根据用户ID获取用户详细信息。

**路径参数：**
- `user_id` (int): 用户ID

**响应：**
- **200 OK** - 获取成功
- **404 Not Found** - 用户不存在

### 4. 更新用户

**PUT** `/api/v1/users/{user_id}`

更新用户信息。

**路径参数：**
- `user_id` (int): 用户ID

**请求体：**
```json
{
  "username": "string",      // 用户名，可选
  "email": "string",         // 邮箱地址，可选
  "full_name": "string",     // 全名，可选
  "avatar_url": "string",    // 头像URL，可选
  "bio": "string",           // 个人简介，可选
  "is_active": true,         // 是否激活，可选
  "is_verified": true        // 是否已验证，可选
}
```

**响应：**
- **200 OK** - 更新成功
- **400 Bad Request** - 请求数据无效
- **404 Not Found** - 用户不存在

### 5. 删除用户

**DELETE** `/api/v1/users/{user_id}`

删除用户账户。

**路径参数：**
- `user_id` (int): 用户ID

**响应：**
- **204 No Content** - 删除成功
- **404 Not Found** - 用户不存在

### 6. 更新密码

**PATCH** `/api/v1/users/{user_id}/password`

更新用户密码。

**路径参数：**
- `user_id` (int): 用户ID

**请求体：**
```json
{
  "current_password": "string",  // 当前密码，必填
  "new_password": "string"       // 新密码，必填
}
```

**响应：**
- **200 OK** - 密码更新成功
- **400 Bad Request** - 当前密码错误或新密码不符合要求
- **404 Not Found** - 用户不存在

### 7. 激活用户

**PATCH** `/api/v1/users/{user_id}/activate`

激活用户账户。

**路径参数：**
- `user_id` (int): 用户ID

**响应：**
- **200 OK** - 激活成功
- **404 Not Found** - 用户不存在

### 8. 停用用户

**PATCH** `/api/v1/users/{user_id}/deactivate`

停用用户账户。

**路径参数：**
- `user_id` (int): 用户ID

**响应：**
- **200 OK** - 停用成功
- **404 Not Found** - 用户不存在

### 9. 验证用户

**PATCH** `/api/v1/users/{user_id}/verify`

验证用户账户。

**路径参数：**
- `user_id` (int): 用户ID

**响应：**
- **200 OK** - 验证成功
- **404 Not Found** - 用户不存在

### 10. 用户认证

**POST** `/api/v1/users/authenticate`

验证用户凭据。

**请求体：**
```json
{
  "username": "string",  // 用户名或邮箱，必填
  "password": "string"   // 密码，必填
}
```

**响应：**
- **200 OK** - 认证成功
- **401 Unauthorized** - 用户名或密码错误

## 数据验证规则

### 用户名
- 长度：3-50字符
- 格式：只能包含字母、数字、下划线和连字符
- 唯一性：系统内唯一

### 邮箱
- 格式：有效的邮箱地址格式
- 唯一性：系统内唯一

### 密码
- 长度：至少8位
- 复杂度：必须包含至少一个大写字母、一个小写字母和一个数字

## 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 使用示例

### 创建用户
```bash
curl -X POST "http://localhost:8000/api/v1/users/" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "full_name": "New User"
  }'
```

### 获取用户列表
```bash
curl -X GET "http://localhost:8000/api/v1/users/?skip=0&limit=10&search=test"
```

### 更新用户
```bash
curl -X PUT "http://localhost:8000/api/v1/users/1" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Updated Name",
    "bio": "Updated bio"
  }'
```
