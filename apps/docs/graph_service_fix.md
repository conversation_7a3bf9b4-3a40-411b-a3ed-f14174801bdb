# 图数据库服务修复说明

## 问题描述

在 Apache AGE 图数据库服务中，出现了系统表查询错误：

```
sqlalchemy.exc.ProgrammingError: relation "pg_wstype" does not exist
```

## 问题原因

在 `graph_service.py` 文件的 `_check_age_availability` 方法中，使用了错误的系统表名称：

```python
# 错误的查询
result = await self.db.execute(text("SELECT 1 FROM pg_wstype WHERE typname = 'agtype'"))
```

`pg_wstype` 不是一个标准的 PostgreSQL 系统表，正确的表名应该是 `pg_type`。

## 解决方案

根据 PostgreSQL 官方文档，修正了系统表查询：

```python
# 正确的查询
result = await self.db.execute(text("SELECT 1 FROM pg_type WHERE typname = 'agtype'"))
```

## 修复内容

1. **修正系统表名称**：将 `pg_wstype` 改为 `pg_type`
2. **保持查询逻辑不变**：仍然检查 `agtype` 数据类型是否存在
3. **确保功能完整性**：Apache AGE 扩展检查功能正常工作

## 技术细节

### PostgreSQL 系统表

- **`pg_type`**：标准的 PostgreSQL 系统表，存储所有数据类型的定义
- **`agtype`**：Apache AGE 扩展定义的数据类型，用于存储图数据

### 查询目的

这个查询用于检查 Apache AGE 扩展是否正确安装并启用了 `agtype` 数据类型，这是 AGE 扩展工作的基础。

## 验证结果

修复后的测试显示：

1. ✅ `pg_type` 系统表查询成功
2. ✅ AGE 扩展已安装
3. ✅ GraphService AGE 可用性检查通过
4. ✅ 图数据库统计信息正常获取

## 影响范围

- Apache AGE 图数据库服务的可用性检查
- 图数据库操作的初始化过程
- 应用启动时的健康检查

## 相关文件

- `apps/services/graph_service.py`：主要的修复文件
- `apps/CHANGELOG.md`：更新日志
- `apps/pyproject.toml`：版本号更新

## 注意事项

1. 这个修复确保了 AGE 扩展检查功能的正常工作
2. 使用标准的 PostgreSQL 系统表查询，提高了代码的可靠性
3. 遵循 PostgreSQL 官方文档的最佳实践
