# 博客系统模型设计文档

## 概述

本文档描述了 EchoNote 个人博客系统的完整数据模型设计，包括所有核心实体、关系映射和设计考虑。

## 模型架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      User       │    │    Category     │    │       Tag       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ username        │    │ name            │    │ name            │
│ email           │    │ slug            │    │ slug            │
│ full_name       │    │ description     │    │ description     │
│ hashed_password │    │ sort_order      │    │ is_active       │
│ is_active       │    │ is_active       │    │ created_at      │
│ is_superuser    │    │ created_at      │    │ updated_at      │
│ is_verified     │    │ updated_at      │    └─────────────────┘
│ avatar_url      │    └─────────────────┘              │
│ bio             │              │                      │
│ created_at      │              │                      │
│ updated_at      │              │                      │
│ last_login_at   │              │                      │
└─────────────────┘              │                      │
         │                       │                      │
         │                       │                      │
         │ 1:N                   │ 1:N                  │
         │                       │                      │
         ▼                       ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      Post       │    │      Page       │    │     Media       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ title           │    │ title           │    │ filename        │
│ slug            │    │ slug            │    │ original_filename│
│ excerpt         │    │ content         │    │ file_path       │
│ content         │    │ meta_title      │    │ file_url        │
│ meta_title      │    │ meta_description│    │ file_size       │
│ meta_description│    │ meta_keywords   │    │ mime_type       │
│ meta_keywords   │    │ template        │    │ file_extension  │
│ status          │    │ is_published    │    │ width           │
│ is_featured     │    │ is_homepage     │    │ height          │
│ allow_comments  │    │ sort_order      │    │ duration        │
│ view_count      │    │ published_at    │    │ title           │
│ like_count      │    │ created_at      │    │ alt_text        │
│ comment_count   │    │ updated_at      │    │ caption         │
│ published_at    │    │ author_id (FK)  │    │ description     │
│ created_at      │    └─────────────────┘    │ is_public       │
│ updated_at      │              │            │ is_featured     │
│ author_id (FK)  │              │            │ created_at      │
│ category_id (FK)│              │            │ updated_at      │
└─────────────────┘              │            │ uploader_id (FK)│
         │                       │            └─────────────────┘
         │                       │                      │
         │ 1:N                   │ 1:N                  │
         │                       │                      │
         ▼                       ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Comment      │    │   MenuItem      │    │   FileUpload    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ content         │    │ title           │    │ filename        │
│ author_name     │    │ url             │    │ original_filename│
│ author_email    │    │ target          │    │ file_path       │
│ author_website  │    │ menu_type       │    │ file_size       │
│ status          │    │ parent_id (FK)  │    │ mime_type       │
│ is_approved     │    │ sort_order      │    │ file_extension  │
│ created_at      │    │ is_active       │    │ upload_session_id│
│ updated_at      │    │ is_external     │    │ chunk_number    │
│ post_id (FK)    │    │ icon            │    │ total_chunks    │
│ author_id (FK)  │    │ css_class       │    │ is_completed    │
│ parent_id (FK)  │    │ created_at      │    │ is_processed    │
└─────────────────┘    │ updated_at      │    │ error_message   │
         │              │ created_by_id (FK)│    │ created_at      │
         │              └─────────────────┘    │ updated_at      │
         │                       │            │ uploader_id (FK)│
         │                       │            └─────────────────┘
         │                       │
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   PostView      │    │    Setting      │
├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │
│ ip_address      │    │ key             │
│ user_agent      │    │ value           │
│ referrer        │    │ description     │
│ viewed_at       │    │ type            │
│ post_id (FK)    │    │ is_public       │
│ user_id (FK)    │    │ is_editable     │
└─────────────────┘    │ group           │
                       │ sort_order      │
                       │ created_at      │
                       │ updated_at      │
                       │ updated_by_id (FK)│
                       └─────────────────┘
```

## 核心模型说明

### 1. 用户模型 (User)
- **功能**: 系统用户管理，支持博客作者和访客
- **关键字段**: 用户名、邮箱、密码哈希、状态标识
- **关系**: 一对多关联文章、评论、页面、媒体文件等

### 2. 文章模型 (Post)
- **功能**: 博客文章的核心内容管理
- **关键字段**: 标题、内容、状态、统计信息、SEO元数据
- **关系**: 
  - 多对一关联作者 (User)
  - 多对一关联分类 (Category)
  - 多对多关联标签 (Tag)
  - 一对多关联评论 (Comment)

### 3. 分类模型 (Category)
- **功能**: 文章分类管理，支持层级结构
- **关键字段**: 名称、别名、描述、排序、状态
- **关系**: 一对多关联文章

### 4. 标签模型 (Tag)
- **功能**: 文章标签管理，支持多标签关联
- **关键字段**: 名称、别名、描述、状态
- **关系**: 多对多关联文章（通过关联表）

### 5. 评论模型 (Comment)
- **功能**: 文章评论系统，支持嵌套回复
- **关键字段**: 内容、作者信息、状态、审核状态
- **关系**: 
  - 多对一关联文章
  - 多对一关联作者（可选）
  - 自引用关联（支持回复）

### 6. 页面模型 (Page)
- **功能**: 静态页面管理，如关于页面、联系页面等
- **关键字段**: 标题、内容、模板、发布状态、首页标识
- **关系**: 多对一关联作者

### 7. 媒体文件模型 (Media)
- **功能**: 媒体文件管理，支持图片、视频、音频等
- **关键字段**: 文件名、路径、大小、MIME类型、元数据
- **关系**: 多对一关联上传者

### 8. 设置模型 (Setting)
- **功能**: 系统配置管理，支持分组和类型化设置
- **关键字段**: 键值对、类型、分组、可编辑性
- **关系**: 多对一关联更新者

### 9. 菜单项模型 (MenuItem)
- **功能**: 网站导航菜单管理，支持多级菜单
- **关键字段**: 标题、URL、目标、菜单类型、排序
- **关系**: 
  - 多对一关联创建者
  - 自引用关联（支持父子菜单）

### 10. 通知模型 (Notification)
- **功能**: 用户通知系统，支持多种通知类型
- **关键字段**: 标题、消息、类型、状态、相关数据
- **关系**: 多对一关联接收者和发送者

## 设计特点

### 1. 关系映射
- **一对多关系**: 使用 `ForeignKey` 和外键约束
- **多对多关系**: 使用关联表 `post_tag_association`
- **自引用关系**: 支持评论回复和菜单层级
- **级联删除**: 合理配置级联规则，保证数据一致性

### 2. 索引优化
- **主键索引**: 所有模型都有主键索引
- **外键索引**: 所有外键字段都建立索引
- **复合索引**: 针对常用查询场景建立复合索引
- **唯一索引**: 确保关键字段的唯一性

### 3. 状态管理
- **软删除**: 使用状态字段而不是物理删除
- **审核机制**: 评论支持审核流程
- **发布控制**: 文章和页面支持草稿和发布状态

### 4. 扩展性设计
- **类型化设置**: 支持不同类型的配置值
- **模板系统**: 页面支持自定义模板
- **媒体类型**: 支持多种媒体文件类型
- **通知类型**: 支持多种通知场景

### 5. 性能考虑
- **分页支持**: 通过排序字段支持高效分页
- **统计字段**: 预计算常用统计信息
- **缓存友好**: 设计支持缓存的数据结构
- **查询优化**: 合理的索引设计

## 使用示例

### 创建文章
```python
# 创建分类
category = Category(name="技术", slug="tech", description="技术相关文章")

# 创建标签
tag1 = Tag(name="Python", slug="python")
tag2 = Tag(name="Web开发", slug="web-dev")

# 创建文章
post = Post(
    title="Python Web开发指南",
    slug="python-web-dev-guide",
    content="文章内容...",
    author=user,
    category=category,
    tags=[tag1, tag2],
    status="published"
)
```

### 查询文章
```python
# 查询已发布的文章
published_posts = session.query(Post).filter(
    Post.status == "published"
).order_by(Post.published_at.desc()).all()

# 查询特定分类的文章
tech_posts = session.query(Post).join(Category).filter(
    Category.slug == "tech",
    Post.status == "published"
).all()

# 查询包含特定标签的文章
python_posts = session.query(Post).join(
    post_tag_association
).join(Tag).filter(
    Tag.slug == "python",
    Post.status == "published"
).all()
```

### 管理评论
```python
# 创建评论
comment = Comment(
    content="很好的文章！",
    author_name="访客",
    author_email="<EMAIL>",
    post=post,
    status="pending"
)

# 回复评论
reply = Comment(
    content="谢谢支持！",
    author_name="作者",
    author_email="<EMAIL>",
    post=post,
    parent=comment,
    status="approved"
)
```

## 总结

这个博客系统模型设计提供了完整的功能支持，包括：

1. **内容管理**: 文章、分类、标签、页面
2. **用户交互**: 评论、通知、浏览统计
3. **媒体管理**: 文件上传、媒体库
4. **系统配置**: 设置、菜单、主题
5. **扩展性**: 支持未来功能扩展

设计遵循了数据库设计的最佳实践，确保了数据完整性、查询性能和系统可维护性。
