#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:18
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 12:45 上下文管理模块初始化

"""
上下文管理模块

提供全局的上下文管理功能，包括：
- 请求ID跟踪
- 用户上下文
- 请求上下文
- 日志上下文
"""

from .context_manager import (
    ContextManager, add_context_callback, clear_context, context_manager, get_context,
    get_request_id_from_context, get_trace_id, get_user_id_from_context, register_context,
    set_context, set_request_id_to_context, set_trace_id, set_user_id_to_context,
)
from .request_context import (
    RequestContext, add_request_extra, clear_request_context, create_request_context,
    get_ip_address, get_request_context, get_request_duration, get_request_extra, get_request_id,
    get_request_method, get_request_path, get_user_agent, get_user_id, get_username,
    set_request_context,
)

__all__ = [
    # 请求上下文
    "RequestContext",
    "get_request_id",
    "get_user_id",
    "get_username",
    "get_ip_address",
    "get_user_agent",
    "get_request_method",
    "get_request_path",
    "get_request_duration",
    "get_request_context",
    "set_request_context",
    "clear_request_context",
    "create_request_context",
    "add_request_extra",
    "get_request_extra",

    # 全局上下文管理器
    "ContextManager",
    "context_manager",
    "get_context",
    "set_context",
    "clear_context",
    "register_context",
    "add_context_callback",
    "get_request_id_from_context",
    "set_request_id_to_context",
    "get_user_id_from_context",
    "set_user_id_to_context",
    "get_trace_id",
    "set_trace_id",
]
