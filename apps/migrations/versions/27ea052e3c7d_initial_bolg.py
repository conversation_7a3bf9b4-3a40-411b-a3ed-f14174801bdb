"""Initial bolg

Revision ID: 27ea052e3c7d
Revises: a4a474f52800
Create Date: 2025-08-07 15:02:32.363396

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '27ea052e3c7d'
down_revision: Union[str, Sequence[str], None] = 'a4a474f52800'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_graph_edges_id'), table_name='graph_edges')
    op.drop_table('graph_edges')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_graph_nodes_id'), table_name='graph_nodes')
    op.drop_table('graph_nodes')
    op.drop_index(op.f('ix_graph_schemas_id'), table_name='graph_schemas')
    op.drop_table('graph_schemas')
    op.drop_index(op.f('ix_embedding_collections_id'), table_name='embedding_collections')
    op.drop_table('embedding_collections')
    op.drop_index(op.f('ix_embeddings_id'), table_name='embeddings')
    op.drop_table('embeddings')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('embeddings',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False, comment='原始内容'),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='标题'),
    sa.Column('embedding', pgvector.sqlalchemy.vector.VECTOR(dim=1536), autoincrement=False, nullable=True, comment='向量嵌入'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('source', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='来源'),
    sa.Column('source_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='来源ID'),
    sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='分类'),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='标签'),
    sa.Column('token_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='token数量'),
    sa.Column('char_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='字符数量'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('embeddings_pkey'))
    )
    op.create_index(op.f('ix_embeddings_id'), 'embeddings', ['id'], unique=False)
    op.create_table('embedding_collections',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='集合名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='描述'),
    sa.Column('dimension', sa.INTEGER(), autoincrement=False, nullable=False, comment='向量维度'),
    sa.Column('distance_metric', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='距离度量方式'),
    sa.Column('embedding_count', sa.INTEGER(), autoincrement=False, nullable=False, comment='向量数量'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('embedding_collections_pkey')),
    sa.UniqueConstraint('name', name=op.f('embedding_collections_name_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_embedding_collections_id'), 'embedding_collections', ['id'], unique=False)
    op.create_table('graph_schemas',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('graph_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='图名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='图描述'),
    sa.Column('node_labels', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='节点标签列表'),
    sa.Column('edge_labels', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='边标签列表'),
    sa.Column('schema_definition', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='模式定义'),
    sa.Column('node_count', sa.INTEGER(), autoincrement=False, nullable=False, comment='节点数量'),
    sa.Column('edge_count', sa.INTEGER(), autoincrement=False, nullable=False, comment='边数量'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='是否激活'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('graph_schemas_pkey')),
    sa.UniqueConstraint('graph_name', name=op.f('graph_schemas_graph_name_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_graph_schemas_id'), 'graph_schemas', ['id'], unique=False)
    op.create_table('graph_nodes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('graph_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='图名称'),
    sa.Column('node_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='节点ID'),
    sa.Column('label', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='节点标签'),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='节点名称'),
    sa.Column('properties', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='节点属性'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('in_degree', sa.INTEGER(), autoincrement=False, nullable=False, comment='入度'),
    sa.Column('out_degree', sa.INTEGER(), autoincrement=False, nullable=False, comment='出度'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('graph_nodes_pkey'))
    )
    op.create_index(op.f('ix_graph_nodes_id'), 'graph_nodes', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('username', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('email', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('full_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('hashed_password', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_superuser', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_verified', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('avatar_url', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('bio', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('last_login_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('users_pkey'))
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_table('graph_edges',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('graph_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='图名称'),
    sa.Column('edge_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='边ID'),
    sa.Column('label', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='边标签'),
    sa.Column('source_node_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='源节点ID'),
    sa.Column('target_node_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='目标节点ID'),
    sa.Column('properties', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='边属性'),
    sa.Column('weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='边权重'),
    sa.Column('is_directed', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='是否有向'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('graph_edges_pkey'))
    )
    op.create_index(op.f('ix_graph_edges_id'), 'graph_edges', ['id'], unique=False)
    # ### end Alembic commands ###
