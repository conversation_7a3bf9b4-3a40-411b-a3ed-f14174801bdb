# EchoNote 更新日志

## [0.1.6] - 2025-01-27

### Added
- 新增：完整的个人博客系统数据模型设计
  - 创建了博客核心模型：`Post`（文章）、`Category`（分类）、`Tag`（标签）、`Comment`（评论）
  - 创建了页面和媒体文件模型：`Page`（静态页面）、`Media`（媒体文件）、`FileUpload`（文件上传）
  - 创建了设置和菜单模型：`Setting`（系统设置）、`MenuItem`（菜单项）、`ThemeSetting`（主题设置）、`Notification`（通知）
  - 实现了完整的模型关系映射，包括一对多、多对多、自引用等关系
  - 添加了详细的类型注解、索引优化和级联删除配置

### Technical Details
- 模型设计遵循 SQLAlchemy 2.0 最佳实践，使用 `Mapped` 类型注解
- 实现了完整的博客系统功能，包括文章管理、分类标签、评论系统、媒体管理
- 支持多级菜单、主题设置、通知系统等高级功能
- 所有模型都包含 `to_dict()` 方法，便于序列化
- 添加了适当的数据库索引以提高查询性能
- 实现了软删除和状态管理机制

### Changed
- 修改：更新了用户模型，添加了与博客系统的关系映射
- 修改：更新了模型模块的 `__init__.py`，导出所有新的博客模型

## [0.1.5] - 2025-01-27

### Fixed
- 修复：Apache AGE Cypher 查询中的列定义不匹配错误
  - 修正了 `graph_service.py` 中多列查询的 SQL 语法
  - 动态构建列定义以匹配 Cypher 查询的返回列数
  - 改进了结果解析逻辑以处理单列和多列结果

### Technical Details
- 问题原因：Cypher 查询返回多个列时，SQL 的 `AS` 子句只定义了一个列
- 解决方案：根据查询的 `RETURN` 子句动态构建列定义
- 影响范围：Apache AGE 图数据库的统计查询功能

## [0.1.4] - 2025-01-27

### Fixed
- 修复：图数据库服务中的系统表查询错误
  - 修正了 `graph_service.py` 中查询 `agtype` 数据类型的系统表名称
  - 将错误的 `pg_wstype` 改为正确的 `pg_type` 系统表
  - 确保 Apache AGE 扩展检查功能正常工作

### Technical Details
- 问题原因：使用了不存在的系统表 `pg_wstype`，正确的表名应该是 `pg_type`
- 解决方案：使用标准的 PostgreSQL 系统表 `pg_type` 来查询数据类型信息
- 影响范围：Apache AGE 图数据库服务的可用性检查

## [0.1.3] - 2025-01-27

### Fixed
- 修复：PostgreSQL 数据库初始化脚本中的语法错误
  - 修正了 `init-db.sql` 中 DO 语句的异常处理语法
  - 移除了嵌套的 BEGIN-END 块，使用正确的 EXCEPTION 语法
  - 确保 Apache AGE 图数据库能够正确创建

### Technical Details
- 问题原因：PostgreSQL DO 语句中嵌套的 BEGIN-END 块导致语法错误
- 解决方案：简化异常处理结构，使用标准的 EXCEPTION 语法
- 影响范围：数据库初始化过程，Apache AGE 图数据库创建

## [0.1.2] - 2025-01-27

### Added
- 新增：增强日志器功能，自动为所有 `logger.error` 调用添加 `exc_info=True`
  - 创建了 `EnhancedLogger` 类，继承自 `logging.Logger`
  - 重写了 `error` 方法，自动添加异常堆栈信息
  - 重写了 `exception` 方法，确保始终包含异常信息
  - 支持手动覆盖 `exc_info` 参数

### Technical Details
- 实现方式：使用 `logging.setLoggerClass(EnhancedLogger)` 注册自定义日志器类
- 功能特点：自动为错误日志添加完整的异常堆栈跟踪信息
- 兼容性：支持手动指定 `exc_info=False` 来禁用自动添加的异常信息
- 影响范围：所有通过 `get_logger()` 获取的日志器实例

## [0.1.1] - 2025-01-27

### Fixed
- 修复：日志配置中 `request_id` 字段缺失导致的格式化错误
  - 添加了 `SafeLogFormatter` 类来处理缺失的日志字段
  - 修改了日志中间件，确保在应用启动时也能正常记录日志
  - 统一了日志格式管理，避免重复配置

### Changed
- 修改：日志格式现在使用 `request_aware` 格式化器作为默认格式
- 修改：日志配置模块现在统一管理所有格式化器配置

### Technical Details
- 问题原因：在应用启动过程中（如 uvicorn 启动日志），还没有请求上下文，导致 `request_id` 等字段不存在
- 解决方案：创建安全的日志格式化器，在格式化前检查并设置默认值
- 影响范围：所有使用日志记录的地方，包括应用启动、请求处理等场景
