#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 12:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : blog_examples.py
# @Update  : 2025/1/27 12:00 博客系统使用示例

"""
博客系统使用示例

展示如何使用博客系统的各种模型进行常见操作，
包括创建、查询、更新和删除数据。
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import create_engine, select, update, delete
from sqlalchemy.orm import Session, joinedload, selectinload

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import (
    User, Category, Tag, Post, Comment, PostView, 
    Page, Media, FileUpload, Setting, MenuItem, 
    ThemeSetting, Notification, post_tag_association
)
from configs.database import Base


class BlogSystemExamples:
    """博客系统使用示例类"""
    
    def __init__(self, database_url: str = "sqlite:///blog_example.db"):
        """初始化示例系统"""
        self.engine = create_engine(database_url, echo=True)
        Base.metadata.create_all(self.engine)
        self.session = Session(self.engine)
    
    def create_sample_data(self) -> None:
        """创建示例数据"""
        print("🚀 开始创建示例数据...")
        
        # 创建用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="系统管理员",
            hashed_password="hashed_password_here",
            is_active=True,
            is_superuser=True,
            is_verified=True,
            bio="系统管理员，负责博客系统的管理。"
        )
        
        author_user = User(
            username="author",
            email="<EMAIL>",
            full_name="博客作者",
            hashed_password="hashed_password_here",
            is_active=True,
            is_superuser=False,
            is_verified=True,
            bio="专业的技术博客作者，专注于Python和Web开发。"
        )
        
        self.session.add_all([admin_user, author_user])
        self.session.commit()
        
        # 创建分类
        tech_category = Category(
            name="技术",
            slug="tech",
            description="技术相关文章",
            sort_order=1,
            is_active=True
        )
        
        life_category = Category(
            name="生活",
            slug="life",
            description="生活感悟和随笔",
            sort_order=2,
            is_active=True
        )
        
        self.session.add_all([tech_category, life_category])
        self.session.commit()
        
        # 创建标签
        python_tag = Tag(name="Python", slug="python", description="Python编程语言")
        web_tag = Tag(name="Web开发", slug="web-dev", description="Web开发技术")
        django_tag = Tag(name="Django", slug="django", description="Django框架")
        flask_tag = Tag(name="Flask", slug="flask", description="Flask框架")
        
        self.session.add_all([python_tag, web_tag, django_tag, flask_tag])
        self.session.commit()
        
        # 创建文章
        post1 = Post(
            title="Python Web开发入门指南",
            slug="python-web-dev-guide",
            excerpt="本文介绍Python Web开发的基础知识，包括框架选择、开发环境搭建等。",
            content="""
# Python Web开发入门指南

Python是一门优秀的编程语言，特别适合Web开发。本文将介绍Python Web开发的基础知识。

## 1. 框架选择

Python有很多优秀的Web框架，主要包括：

- **Django**: 全功能框架，适合大型项目
- **Flask**: 轻量级框架，适合小型项目
- **FastAPI**: 现代异步框架，适合API开发

## 2. 开发环境搭建

### 安装Python
首先确保你的系统已安装Python 3.8或更高版本。

### 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\\Scripts\\activate  # Windows
```

### 安装依赖
```bash
pip install django
# 或
pip install flask
```

## 3. 第一个项目

让我们创建一个简单的Flask应用：

```python
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return 'Hello, World!'

if __name__ == '__main__':
    app.run(debug=True)
```

## 总结

Python Web开发入门并不困难，选择合适的框架和工具是关键。
            """,
            meta_title="Python Web开发入门指南 - 完整教程",
            meta_description="学习Python Web开发的基础知识，包括框架选择、环境搭建和第一个项目。",
            meta_keywords="Python,Web开发,Django,Flask,教程",
            status="published",
            is_featured=True,
            allow_comments=True,
            published_at=datetime.utcnow(),
            author=author_user,
            category=tech_category,
            tags=[python_tag, web_tag]
        )
        
        post2 = Post(
            title="Django vs Flask: 如何选择合适的框架",
            slug="django-vs-flask-comparison",
            excerpt="详细比较Django和Flask两个主流Python Web框架的优缺点。",
            content="""
# Django vs Flask: 如何选择合适的框架

Django和Flask是Python Web开发中最受欢迎的两个框架，它们各有特点。

## Django 特点

### 优点
- **全功能**: 内置ORM、Admin后台、认证系统等
- **快速开发**: 提供大量现成的组件
- **安全性**: 内置CSRF保护、XSS防护等
- **文档完善**: 官方文档详细，社区活跃

### 缺点
- **学习曲线**: 相对复杂，需要时间掌握
- **灵活性**: 框架约束较多，不够灵活
- **性能**: 相比轻量级框架，性能略低

## Flask 特点

### 优点
- **轻量级**: 核心简单，易于理解
- **灵活性**: 高度可定制，适合各种需求
- **学习简单**: 入门容易，快速上手
- **性能好**: 轻量级，性能优秀

### 缺点
- **功能有限**: 需要自己集成各种组件
- **开发速度**: 需要更多时间搭建基础架构
- **安全性**: 需要自己实现安全措施

## 选择建议

- **选择Django**: 大型项目、团队开发、快速原型
- **选择Flask**: 小型项目、API服务、学习目的

## 总结

没有最好的框架，只有最适合的框架。根据项目需求选择合适的工具。
            """,
            meta_title="Django vs Flask 框架对比 - 选择指南",
            meta_description="详细对比Django和Flask框架，帮助你选择最适合的Python Web框架。",
            meta_keywords="Django,Flask,Python,Web框架,对比",
            status="published",
            is_featured=False,
            allow_comments=True,
            published_at=datetime.utcnow(),
            author=author_user,
            category=tech_category,
            tags=[python_tag, django_tag, flask_tag]
        )
        
        self.session.add_all([post1, post2])
        self.session.commit()
        
        # 创建评论
        comment1 = Comment(
            content="非常详细的教程，对初学者很有帮助！",
            author_name="学习Python的小白",
            author_email="<EMAIL>",
            status="approved",
            is_approved=True,
            post=post1
        )
        
        comment2 = Comment(
            content="感谢分享，让我对框架选择有了更清晰的认识。",
            author_name="Web开发者",
            author_email="<EMAIL>",
            status="approved",
            is_approved=True,
            post=post2
        )
        
        reply1 = Comment(
            content="谢谢支持，我会继续分享更多有用的内容！",
            author_name="博客作者",
            author_email="<EMAIL>",
            status="approved",
            is_approved=True,
            post=post1,
            parent=comment1,
            author=author_user
        )
        
        self.session.add_all([comment1, comment2, reply1])
        self.session.commit()
        
        # 创建页面
        about_page = Page(
            title="关于我们",
            slug="about",
            content="""
# 关于我们

我们是一个专注于技术分享的博客平台，致力于为开发者提供有价值的技术内容。

## 我们的使命

- 分享最新的技术动态
- 提供实用的开发教程
- 建立技术交流社区
- 促进技术知识传播

## 联系方式

- 邮箱: <EMAIL>
- 微信: tech_blog
- GitHub: https://github.com/techblog
            """,
            meta_title="关于我们 - 技术博客",
            meta_description="了解我们的使命和联系方式。",
            template="about",
            is_published=True,
            sort_order=1,
            author=admin_user
        )
        
        self.session.add(about_page)
        self.session.commit()
        
        # 创建设置
        site_settings = [
            Setting(key="site_name", value="技术博客", description="网站名称", group="general"),
            Setting(key="site_description", value="专注于技术分享的博客平台", description="网站描述", group="general"),
            Setting(key="posts_per_page", value="10", description="每页显示文章数", type="integer", group="content"),
            Setting(key="allow_comments", value="true", description="是否允许评论", type="boolean", group="content"),
            Setting(key="comment_moderation", value="true", description="评论需要审核", type="boolean", group="content"),
        ]
        
        for setting in site_settings:
            setting.updated_by_id = admin_user.id
        
        self.session.add_all(site_settings)
        self.session.commit()
        
        # 创建菜单
        main_menu = [
            MenuItem(title="首页", url="/", menu_type="main", sort_order=1, created_by=admin_user),
            MenuItem(title="技术", url="/category/tech", menu_type="main", sort_order=2, created_by=admin_user),
            MenuItem(title="生活", url="/category/life", menu_type="main", sort_order=3, created_by=admin_user),
            MenuItem(title="关于", url="/page/about", menu_type="main", sort_order=4, created_by=admin_user),
        ]
        
        self.session.add_all(main_menu)
        self.session.commit()
        
        print("✅ 示例数据创建完成！")
    
    def demonstrate_queries(self) -> None:
        """演示各种查询操作"""
        print("\n🔍 演示查询操作...")
        
        # 1. 查询所有已发布的文章
        published_posts = self.session.query(Post).filter(
            Post.status == "published"
        ).order_by(Post.published_at.desc()).all()
        
        print(f"📝 已发布文章数: {len(published_posts)}")
        for post in published_posts:
            print(f"  - {post.title} (作者: {post.author.full_name})")
        
        # 2. 查询特定分类的文章
        tech_posts = self.session.query(Post).join(Category).filter(
            Category.slug == "tech",
            Post.status == "published"
        ).all()
        
        print(f"\n🔧 技术分类文章数: {len(tech_posts)}")
        
        # 3. 查询包含特定标签的文章
        python_posts = self.session.query(Post).join(
            post_tag_association
        ).join(Tag).filter(
            Tag.slug == "python",
            Post.status == "published"
        ).all()
        
        print(f"🐍 Python标签文章数: {len(python_posts)}")
        
        # 4. 查询文章及其评论（使用joinedload）
        posts_with_comments = self.session.query(Post).options(
            joinedload(Post.comments)
        ).filter(
            Post.status == "published"
        ).limit(3).all()
        
        print(f"\n💬 文章及其评论:")
        for post in posts_with_comments:
            print(f"  📄 {post.title}")
            for comment in post.comments:
                print(f"    💭 {comment.author_name}: {comment.content[:50]}...")
        
        # 5. 查询用户及其文章
        users_with_posts = self.session.query(User).options(
            selectinload(User.posts)
        ).filter(
            User.is_active == True
        ).all()
        
        print(f"\n👥 用户及其文章:")
        for user in users_with_posts:
            print(f"  👤 {user.full_name} ({user.username})")
            for post in user.posts:
                print(f"    📝 {post.title} - {post.status}")
        
        # 6. 统计信息
        total_users = self.session.query(User).count()
        total_posts = self.session.query(Post).count()
        total_comments = self.session.query(Comment).count()
        total_categories = self.session.query(Category).count()
        total_tags = self.session.query(Tag).count()
        
        print(f"\n📊 统计信息:")
        print(f"  - 用户数: {total_users}")
        print(f"  - 文章数: {total_posts}")
        print(f"  - 评论数: {total_comments}")
        print(f"  - 分类数: {total_categories}")
        print(f"  - 标签数: {total_tags}")
    
    def demonstrate_updates(self) -> None:
        """演示更新操作"""
        print("\n✏️ 演示更新操作...")
        
        # 1. 更新文章状态
        draft_posts = self.session.query(Post).filter(
            Post.status == "draft"
        ).all()
        
        for post in draft_posts:
            post.status = "published"
            post.published_at = datetime.utcnow()
        
        self.session.commit()
        print(f"✅ 将 {len(draft_posts)} 篇草稿文章发布")
        
        # 2. 更新文章浏览次数
        featured_post = self.session.query(Post).filter(
            Post.is_featured == True
        ).first()
        
        if featured_post:
            featured_post.view_count += 100
            featured_post.like_count += 10
            self.session.commit()
            print(f"✅ 更新推荐文章统计: 浏览 {featured_post.view_count}, 点赞 {featured_post.like_count}")
        
        # 3. 批量更新设置
        self.session.execute(
            update(Setting).where(
                Setting.key == "posts_per_page"
            ).values(value="15")
        )
        self.session.commit()
        print("✅ 更新每页文章数为15")
    
    def demonstrate_deletes(self) -> None:
        """演示删除操作"""
        print("\n🗑️ 演示删除操作...")
        
        # 1. 删除未审核的评论
        pending_comments = self.session.query(Comment).filter(
            Comment.status == "pending"
        ).all()
        
        for comment in pending_comments:
            self.session.delete(comment)
        
        self.session.commit()
        print(f"✅ 删除 {len(pending_comments)} 条待审核评论")
        
        # 2. 软删除（更新状态）
        inactive_categories = self.session.query(Category).filter(
            Category.is_active == False
        ).all()
        
        for category in inactive_categories:
            category.is_active = True  # 重新激活
        
        self.session.commit()
        print(f"✅ 重新激活 {len(inactive_categories)} 个分类")
    
    def demonstrate_advanced_queries(self) -> None:
        """演示高级查询"""
        print("\n🚀 演示高级查询...")
        
        # 1. 查询热门文章（按浏览次数排序）
        popular_posts = self.session.query(Post).filter(
            Post.status == "published"
        ).order_by(Post.view_count.desc()).limit(5).all()
        
        print("🔥 热门文章:")
        for i, post in enumerate(popular_posts, 1):
            print(f"  {i}. {post.title} (浏览: {post.view_count})")
        
        # 2. 查询最新评论
        recent_comments = self.session.query(Comment).filter(
            Comment.status == "approved"
        ).order_by(Comment.created_at.desc()).limit(5).all()
        
        print("\n💬 最新评论:")
        for comment in recent_comments:
            print(f"  - {comment.author_name}: {comment.content[:30]}...")
        
        # 3. 查询标签使用统计
        from sqlalchemy import func
        
        tag_stats = self.session.query(
            Tag.name,
            func.count(post_tag_association.c.post_id).label('post_count')
        ).join(
            post_tag_association
        ).group_by(
            Tag.id, Tag.name
        ).order_by(
            func.count(post_tag_association.c.post_id).desc()
        ).all()
        
        print("\n🏷️ 标签使用统计:")
        for tag_name, post_count in tag_stats:
            print(f"  - {tag_name}: {post_count} 篇文章")
        
        # 4. 查询用户活跃度
        user_activity = self.session.query(
            User.username,
            User.full_name,
            func.count(Post.id).label('post_count'),
            func.count(Comment.id).label('comment_count')
        ).outerjoin(
            Post
        ).outerjoin(
            Comment
        ).group_by(
            User.id, User.username, User.full_name
        ).order_by(
            func.count(Post.id).desc()
        ).all()
        
        print("\n👥 用户活跃度:")
        for username, full_name, post_count, comment_count in user_activity:
            print(f"  - {full_name} ({username}): {post_count} 篇文章, {comment_count} 条评论")
    
    def run_all_examples(self) -> None:
        """运行所有示例"""
        try:
            self.create_sample_data()
            self.demonstrate_queries()
            self.demonstrate_updates()
            self.demonstrate_deletes()
            self.demonstrate_advanced_queries()
            
            print("\n🎉 所有示例运行完成！")
            
        except Exception as e:
            print(f"❌ 运行示例时发生错误: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            self.session.close()


def main():
    """主函数"""
    print("🚀 博客系统使用示例")
    print("=" * 50)
    
    # 创建示例实例
    examples = BlogSystemExamples()
    
    # 运行所有示例
    examples.run_all_examples()


if __name__ == "__main__":
    main()
