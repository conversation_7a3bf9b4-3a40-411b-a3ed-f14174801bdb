#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 10:30
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : page.py
# @Update  : 2025/1/27 10:30 页面和媒体文件模型

"""
页面和媒体文件模型

包含博客系统的页面和媒体文件管理：
- 静态页面 (Page)
- 媒体文件 (Media)
- 文件上传记录 (FileUpload)
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Boolean, DateTime, ForeignKey, Integer, String, Text, 
    Index, UniqueConstraint
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from configs.database import Base


class Page(Base):
    """静态页面模型"""
    
    __tablename__ = "pages"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    title: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    slug: Mapped[str] = mapped_column(String(200), unique=True, nullable=False, index=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    
    # 元数据
    meta_title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    meta_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    meta_keywords: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # 页面设置
    template: Mapped[str] = mapped_column(String(100), default="default", nullable=False)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_homepage: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # 时间信息
    published_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, index=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    author_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    
    # 关系
    author: Mapped["User"] = relationship("User", back_populates="pages")
    
    # 索引
    __table_args__ = (
        Index("idx_pages_status_published_at", "is_published", "published_at"),
        Index("idx_pages_author_status", "author_id", "is_published"),
        UniqueConstraint("is_homepage", name="uq_pages_homepage"),
    )
    
    def __repr__(self) -> str:
        return f"<Page(id={self.id}, title='{self.title}', slug='{self.slug}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "slug": self.slug,
            "content": self.content,
            "meta_title": self.meta_title,
            "meta_description": self.meta_description,
            "meta_keywords": self.meta_keywords,
            "template": self.template,
            "is_published": self.is_published,
            "is_homepage": self.is_homepage,
            "sort_order": self.sort_order,
            "author_id": self.author_id,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Media(Base):
    """媒体文件模型"""
    
    __tablename__ = "media"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    filename: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    original_filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(String(500), nullable=False)
    file_url: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # 文件信息
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)  # 字节
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    file_extension: Mapped[str] = mapped_column(String(20), nullable=False)
    
    # 媒体信息（图片/视频）
    width: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    height: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    duration: Mapped[Optional[float]] = mapped_column(Integer, nullable=True)  # 秒
    
    # 元数据
    title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    alt_text: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    caption: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 状态
    is_public: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_featured: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    uploader_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    
    # 关系
    uploader: Mapped["User"] = relationship("User", back_populates="media")
    
    # 索引
    __table_args__ = (
        Index("idx_media_uploader_created", "uploader_id", "created_at"),
        Index("idx_media_mime_type", "mime_type"),
        Index("idx_media_file_extension", "file_extension"),
    )
    
    def __repr__(self) -> str:
        return f"<Media(id={self.id}, filename='{self.filename}', mime_type='{self.mime_type}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_path": self.file_path,
            "file_url": self.file_url,
            "file_size": self.file_size,
            "mime_type": self.mime_type,
            "file_extension": self.file_extension,
            "width": self.width,
            "height": self.height,
            "duration": self.duration,
            "title": self.title,
            "alt_text": self.alt_text,
            "caption": self.caption,
            "description": self.description,
            "is_public": self.is_public,
            "is_featured": self.is_featured,
            "uploader_id": self.uploader_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @property
    def is_image(self) -> bool:
        """是否为图片文件"""
        return self.mime_type.startswith("image/")
    
    @property
    def is_video(self) -> bool:
        """是否为视频文件"""
        return self.mime_type.startswith("video/")
    
    @property
    def is_audio(self) -> bool:
        """是否为音频文件"""
        return self.mime_type.startswith("audio/")
    
    @property
    def file_size_mb(self) -> float:
        """文件大小（MB）"""
        return self.file_size / (1024 * 1024)


class FileUpload(Base):
    """文件上传记录模型"""
    
    __tablename__ = "file_uploads"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    filename: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    original_filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # 文件信息
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    file_extension: Mapped[str] = mapped_column(String(20), nullable=False)
    
    # 上传信息
    upload_session_id: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    chunk_number: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    total_chunks: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    
    # 状态
    is_completed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    uploader_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    
    # 关系
    uploader: Mapped["User"] = relationship("User", back_populates="file_uploads")
    
    # 索引
    __table_args__ = (
        Index("idx_file_uploads_session", "upload_session_id", "chunk_number"),
        Index("idx_file_uploads_uploader_status", "uploader_id", "is_completed"),
    )
    
    def __repr__(self) -> str:
        return f"<FileUpload(id={self.id}, filename='{self.filename}', session='{self.upload_session_id}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "mime_type": self.mime_type,
            "file_extension": self.file_extension,
            "upload_session_id": self.upload_session_id,
            "chunk_number": self.chunk_number,
            "total_chunks": self.total_chunks,
            "is_completed": self.is_completed,
            "is_processed": self.is_processed,
            "error_message": self.error_message,
            "uploader_id": self.uploader_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
