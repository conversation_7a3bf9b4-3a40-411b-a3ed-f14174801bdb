#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 11:05
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : graph.py
# @Update  : 2025/8/7 11:05 图数据库模型

from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import DateTime, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column

from configs.database import Base


class GraphNode(Base):
    """图节点模型（用于存储图节点的元数据）"""

    __tablename__ = "graph_nodes"

    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # 图信息
    graph_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="图名称")
    node_id: Mapped[str] = mapped_column(String(255), nullable=False, comment="节点ID")

    # 节点信息
    label: Mapped[str] = mapped_column(String(100), nullable=False, comment="节点标签")
    name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="节点名称")

    # 属性
    properties: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="节点属性"
        )

    # 元数据
    meta_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="元数据"
        )

    # 统计信息
    in_degree: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="入度")
    out_degree: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="出度")

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self) -> str:
        return f"<GraphNode(id={self.id}, label='{self.label}', name='{self.name}')>"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id":         self.id,
            "graph_name": self.graph_name,
            "node_id":    self.node_id,
            "label":      self.label,
            "name":       self.name,
            "properties": self.properties,
            "meta_data":  self.meta_data,
            "in_degree":  self.in_degree,
            "out_degree": self.out_degree,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class GraphEdge(Base):
    """图边模型（用于存储图边的元数据）"""

    __tablename__ = "graph_edges"

    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # 图信息
    graph_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="图名称")
    edge_id: Mapped[str] = mapped_column(String(255), nullable=False, comment="边ID")

    # 边信息
    label: Mapped[str] = mapped_column(String(100), nullable=False, comment="边标签")
    source_node_id: Mapped[str] = mapped_column(String(255), nullable=False, comment="源节点ID")
    target_node_id: Mapped[str] = mapped_column(String(255), nullable=False, comment="目标节点ID")

    # 属性
    properties: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="边属性"
        )

    # 权重和方向
    weight: Mapped[Optional[float]] = mapped_column(nullable=True, comment="边权重")
    is_directed: Mapped[bool] = mapped_column(default=True, nullable=False, comment="是否有向")

    # 元数据
    meta_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="元数据"
        )

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self) -> str:
        return f"<GraphEdge(id={self.id}, label='{self.label}', {self.source_node_id}->{self.target_node_id})>"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id":             self.id,
            "graph_name":     self.graph_name,
            "edge_id":        self.edge_id,
            "label":          self.label,
            "source_node_id": self.source_node_id,
            "target_node_id": self.target_node_id,
            "properties":     self.properties,
            "weight":         self.weight,
            "is_directed":    self.is_directed,
            "meta_data":      self.meta_data,
            "created_at":     self.created_at.isoformat() if self.created_at else None,
            "updated_at":     self.updated_at.isoformat() if self.updated_at else None,
        }


class GraphSchema(Base):
    """图模式模型"""

    __tablename__ = "graph_schemas"

    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # 图信息
    graph_name: Mapped[str] = mapped_column(
        String(100), unique=True, nullable=False, comment="图名称"
        )
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="图描述")

    # 模式定义
    node_labels: Mapped[List[str]] = mapped_column(JSONB, nullable=True, comment="节点标签列表")
    edge_labels: Mapped[List[str]] = mapped_column(JSONB, nullable=True, comment="边标签列表")
    schema_definition: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="模式定义"
        )

    # 统计信息
    node_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="节点数量")
    edge_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False, comment="边数量")

    # 配置
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False, comment="是否激活")

    # 元数据
    meta_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, comment="元数据"
        )

    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        comment="更新时间"
    )

    def __repr__(self) -> str:
        return f"<GraphSchema(id={self.id}, graph_name='{self.graph_name}', nodes={self.node_count}, edges={self.edge_count})>"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id":                self.id,
            "graph_name":        self.graph_name,
            "description":       self.description,
            "node_labels":       self.node_labels,
            "edge_labels":       self.edge_labels,
            "schema_definition": self.schema_definition,
            "node_count":        self.node_count,
            "edge_count":        self.edge_count,
            "is_active":         self.is_active,
            "meta_data":         self.meta_data,
            "created_at":        self.created_at.isoformat() if self.created_at else None,
            "updated_at":        self.updated_at.isoformat() if self.updated_at else None,
        }
