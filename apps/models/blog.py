#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 10:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : blog.py
# @Update  : 2025/1/27 10:00 博客系统模型

"""
博客系统模型

包含博客系统的核心数据模型：
- 文章 (Post)
- 分类 (Category)
- 标签 (Tag)
- 评论 (Comment)
- 文章标签关联 (PostTag)
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    Boolean, DateTime, ForeignKey, Integer, String, Text, 
    Table, Column, UniqueConstraint, Index
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from configs.database import Base


# 文章标签关联表（多对多关系）
post_tag_association = Table(
    "post_tag_association",
    Base.metadata,
    Column("post_id", Integer, ForeignKey("posts.id", ondelete="CASCADE"), primary_key=True),
    Column("tag_id", Integer, <PERSON><PERSON>ey("tags.id", ondelete="CASCADE"), primary_key=True),
    Index("idx_post_tag_post_id", "post_id"),
    Index("idx_post_tag_tag_id", "tag_id"),
)


class Category(Base):
    """分类模型"""
    
    __tablename__ = "categories"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    slug: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 排序和状态
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 关系
    posts: Mapped[List["Post"]] = relationship(
        "Post", back_populates="category", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Category(id={self.id}, name='{self.name}', slug='{self.slug}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "sort_order": self.sort_order,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Tag(Base):
    """标签模型"""
    
    __tablename__ = "tags"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    slug: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 关系
    posts: Mapped[List["Post"]] = relationship(
        "Post", secondary=post_tag_association, back_populates="tags"
    )
    
    def __repr__(self) -> str:
        return f"<Tag(id={self.id}, name='{self.name}', slug='{self.slug}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Post(Base):
    """文章模型"""
    
    __tablename__ = "posts"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    title: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    slug: Mapped[str] = mapped_column(String(200), unique=True, nullable=False, index=True)
    excerpt: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    
    # 元数据
    meta_title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    meta_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    meta_keywords: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # 状态和设置
    status: Mapped[str] = mapped_column(
        String(20), default="draft", nullable=False, index=True
    )  # draft, published, archived
    is_featured: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    allow_comments: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 统计信息
    view_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    like_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    comment_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # 时间信息
    published_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, index=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    author_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    category_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("categories.id", ondelete="SET NULL"), nullable=True, index=True
    )
    
    # 关系
    author: Mapped["User"] = relationship("User", back_populates="posts")
    category: Mapped[Optional[Category]] = relationship("Category", back_populates="posts")
    tags: Mapped[List[Tag]] = relationship(
        "Tag", secondary=post_tag_association, back_populates="posts"
    )
    comments: Mapped[List["Comment"]] = relationship(
        "Comment", back_populates="post", cascade="all, delete-orphan"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_posts_status_published_at", "status", "published_at"),
        Index("idx_posts_author_status", "author_id", "status"),
        Index("idx_posts_category_status", "category_id", "status"),
    )
    
    def __repr__(self) -> str:
        return f"<Post(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "slug": self.slug,
            "excerpt": self.excerpt,
            "content": self.content,
            "meta_title": self.meta_title,
            "meta_description": self.meta_description,
            "meta_keywords": self.meta_keywords,
            "status": self.status,
            "is_featured": self.is_featured,
            "allow_comments": self.allow_comments,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "comment_count": self.comment_count,
            "author_id": self.author_id,
            "category_id": self.category_id,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Comment(Base):
    """评论模型"""
    
    __tablename__ = "comments"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_name: Mapped[str] = mapped_column(String(100), nullable=False)
    author_email: Mapped[str] = mapped_column(String(100), nullable=False)
    author_website: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    
    # 状态
    status: Mapped[str] = mapped_column(
        String(20), default="pending", nullable=False, index=True
    )  # pending, approved, spam
    is_approved: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    post_id: Mapped[int] = mapped_column(
        ForeignKey("posts.id", ondelete="CASCADE"), nullable=False, index=True
    )
    author_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    parent_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("comments.id", ondelete="CASCADE"), nullable=True, index=True
    )
    
    # 关系
    post: Mapped[Post] = relationship("Post", back_populates="comments")
    author: Mapped[Optional["User"]] = relationship("User", back_populates="comments")
    parent: Mapped[Optional["Comment"]] = relationship(
        "Comment", remote_side=[id], back_populates="replies"
    )
    replies: Mapped[List["Comment"]] = relationship(
        "Comment", back_populates="parent", cascade="all, delete-orphan"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_comments_post_status", "post_id", "status"),
        Index("idx_comments_author_status", "author_id", "status"),
    )
    
    def __repr__(self) -> str:
        return f"<Comment(id={self.id}, author='{self.author_name}', status='{self.status}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "content": self.content,
            "author_name": self.author_name,
            "author_email": self.author_email,
            "author_website": self.author_website,
            "status": self.status,
            "is_approved": self.is_approved,
            "post_id": self.post_id,
            "author_id": self.author_id,
            "parent_id": self.parent_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class PostView(Base):
    """文章浏览记录模型（用于统计）"""
    
    __tablename__ = "post_views"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    ip_address: Mapped[str] = mapped_column(String(45), nullable=False)  # 支持IPv6
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    referrer: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 时间戳
    viewed_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False, index=True
    )
    
    # 外键关系
    post_id: Mapped[int] = mapped_column(
        ForeignKey("posts.id", ondelete="CASCADE"), nullable=False, index=True
    )
    user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    
    # 关系
    post: Mapped[Post] = relationship("Post")
    user: Mapped[Optional["User"]] = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_post_views_post_date", "post_id", "viewed_at"),
        Index("idx_post_views_ip_date", "ip_address", "viewed_at"),
    )
    
    def __repr__(self) -> str:
        return f"<PostView(id={self.id}, post_id={self.post_id}, ip='{self.ip_address}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "referrer": self.referrer,
            "post_id": self.post_id,
            "user_id": self.user_id,
            "viewed_at": self.viewed_at.isoformat() if self.viewed_at else None,
        }
