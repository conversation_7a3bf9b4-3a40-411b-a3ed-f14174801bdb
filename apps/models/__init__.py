#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:17
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 10:15 模型模块初始化

"""
模型模块

包含所有数据库模型定义，支持：
- 传统关系型数据模型
- 向量数据库模型（pgvector）
- 图数据库模型（Apache AGE）
"""

from .embedding import Embedding, EmbeddingCollection
from .graph import GraphEdge, GraphNode, GraphSchema
from .user import User
from .blog import Category, Tag, Post, Comment, PostView, post_tag_association
from .page import Page, Media, FileUpload
from .setting import Setting, MenuItem, ThemeSetting, Notification

__all__ = [
    # 用户模型
    "User",

    # 博客系统模型
    "Category",
    "Tag", 
    "Post",
    "Comment",
    "PostView",
    "post_tag_association",
    
    # 页面和媒体文件模型
    "Page",
    "Media",
    "FileUpload",
    
    # 设置和菜单模型
    "Setting",
    "MenuItem",
    "ThemeSetting",
    "Notification",

    # 向量数据库模型
    "Embedding",
    "EmbeddingCollection",

    # 图数据库模型
    "GraphNode",
    "GraphEdge",
    "GraphSchema",
]
