#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:17
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 10:15 模型模块初始化

"""
模型模块

包含所有数据库模型定义，支持：
- 传统关系型数据模型
- 向量数据库模型（pgvector）
- 图数据库模型（Apache AGE）
"""

from .embedding import Embedding, EmbeddingCollection
from .graph import GraphEdge, GraphNode, GraphSchema
from .user import User

__all__ = [
    # 用户模型
    "User",

    # 向量数据库模型
    "Embedding",
    "EmbeddingCollection",

    # 图数据库模型
    "GraphNode",
    "GraphEdge",
    "GraphSchema",
]
