#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 11:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : setting.py
# @Update  : 2025/1/27 11:00 设置和菜单模型

"""
设置和菜单模型

包含博客系统的配置管理：
- 系统设置 (Setting)
- 菜单项 (MenuItem)
- 主题设置 (ThemeSetting)
"""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import (
    Boolean, DateTime, ForeignKey, Integer, String, Text, 
    Index, UniqueConstraint, JSON
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from configs.database import Base


class Setting(Base):
    """系统设置模型"""
    
    __tablename__ = "settings"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    key: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # 设置类型
    type: Mapped[str] = mapped_column(
        String(20), default="string", nullable=False, index=True
    )  # string, integer, boolean, json, text
    is_public: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_editable: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 分组
    group: Mapped[str] = mapped_column(String(50), default="general", nullable=False, index=True)
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    updated_by_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    
    # 关系
    updated_by: Mapped[Optional["User"]] = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_settings_group_order", "group", "sort_order"),
        Index("idx_settings_type", "type"),
    )
    
    def __repr__(self) -> str:
        return f"<Setting(id={self.id}, key='{self.key}', type='{self.type}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "key": self.key,
            "value": self.value,
            "description": self.description,
            "type": self.type,
            "is_public": self.is_public,
            "is_editable": self.is_editable,
            "group": self.group,
            "sort_order": self.sort_order,
            "updated_by_id": self.updated_by_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class MenuItem(Base):
    """菜单项模型"""
    
    __tablename__ = "menu_items"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    title: Mapped[str] = mapped_column(String(100), nullable=False)
    url: Mapped[str] = mapped_column(String(500), nullable=False)
    target: Mapped[str] = mapped_column(
        String(20), default="_self", nullable=False
    )  # _self, _blank, _parent, _top
    
    # 菜单设置
    menu_type: Mapped[str] = mapped_column(
        String(20), default="main", nullable=False, index=True
    )  # main, footer, sidebar, mobile
    parent_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("menu_items.id", ondelete="CASCADE"), nullable=True, index=True
    )
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_external: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 图标和样式
    icon: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    css_class: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    created_by_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    
    # 关系
    created_by: Mapped["User"] = relationship("User", back_populates="menu_items")
    parent: Mapped[Optional["MenuItem"]] = relationship(
        "MenuItem", remote_side=[id], back_populates="children"
    )
    children: Mapped[List["MenuItem"]] = relationship(
        "MenuItem", back_populates="parent", cascade="all, delete-orphan"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_menu_items_type_order", "menu_type", "sort_order"),
        Index("idx_menu_items_parent_order", "parent_id", "sort_order"),
    )
    
    def __repr__(self) -> str:
        return f"<MenuItem(id={self.id}, title='{self.title}', menu_type='{self.menu_type}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "url": self.url,
            "target": self.target,
            "menu_type": self.menu_type,
            "parent_id": self.parent_id,
            "sort_order": self.sort_order,
            "is_active": self.is_active,
            "is_external": self.is_external,
            "icon": self.icon,
            "css_class": self.css_class,
            "created_by_id": self.created_by_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class ThemeSetting(Base):
    """主题设置模型"""
    
    __tablename__ = "theme_settings"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    theme_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    setting_key: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    setting_value: Mapped[str] = mapped_column(Text, nullable=False)
    
    # 设置类型
    setting_type: Mapped[str] = mapped_column(
        String(20), default="string", nullable=False
    )  # string, integer, boolean, json, color, image
    is_public: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # 分组和排序
    group: Mapped[str] = mapped_column(String(50), default="general", nullable=False, index=True)
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    
    # 外键关系
    updated_by_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    
    # 关系
    updated_by: Mapped[Optional["User"]] = relationship("User")
    
    # 索引
    __table_args__ = (
        Index("idx_theme_settings_theme_key", "theme_name", "setting_key"),
        Index("idx_theme_settings_group_order", "theme_name", "group", "sort_order"),
        UniqueConstraint("theme_name", "setting_key", name="uq_theme_settings_key"),
    )
    
    def __repr__(self) -> str:
        return f"<ThemeSetting(id={self.id}, theme='{self.theme_name}', key='{self.setting_key}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "theme_name": self.theme_name,
            "setting_key": self.setting_key,
            "setting_value": self.setting_value,
            "setting_type": self.setting_type,
            "is_public": self.is_public,
            "group": self.group,
            "sort_order": self.sort_order,
            "updated_by_id": self.updated_by_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Notification(Base):
    """通知模型"""
    
    __tablename__ = "notifications"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # 基本信息
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    notification_type: Mapped[str] = mapped_column(
        String(20), default="info", nullable=False, index=True
    )  # info, success, warning, error
    
    # 状态
    is_read: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, index=True)
    is_sent: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 相关数据
    related_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # post, comment, user
    related_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    action_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, nullable=False, index=True
    )
    read_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # 外键关系
    recipient_id: Mapped[int] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )
    sender_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True
    )
    
    # 关系
    recipient: Mapped["User"] = relationship(
        "User", foreign_keys=[recipient_id], back_populates="notifications"
    )
    sender: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[sender_id]
    )
    
    # 索引
    __table_args__ = (
        Index("idx_notifications_recipient_read", "recipient_id", "is_read"),
        Index("idx_notifications_type_created", "notification_type", "created_at"),
    )
    
    def __repr__(self) -> str:
        return f"<Notification(id={self.id}, title='{self.title}', type='{self.notification_type}')>"
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "message": self.message,
            "notification_type": self.notification_type,
            "is_read": self.is_read,
            "is_sent": self.is_sent,
            "related_type": self.related_type,
            "related_id": self.related_id,
            "action_url": self.action_url,
            "recipient_id": self.recipient_id,
            "sender_id": self.sender_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "read_at": self.read_at.isoformat() if self.read_at else None,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
        }
