#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : user_routes.py
# @Update  : 2025/8/7 09:45 用户相关API路由

import math
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from configs.database import get_db
from configs.logging_config import get_app_logger
from schemas.user import (
    UserCreate,
    UserListResponse,
    UserLogin,
    UserPasswordUpdate,
    UserResponse,
    UserUpdate
)
from services.user_service import UserService

# 获取应用日志器
logger = get_app_logger()

# 创建路由器
router = APIRouter(prefix="/users", tags=["用户管理"])


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建用户"""
    try:
        user_service = UserService(db)
        user = await user_service.create_user(user_data)
        return UserResponse.model_validate(user)
    except ValueError as e:
        logger.warning(f"创建用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.get("/", response_model=UserListResponse)
async def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=100, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    is_verified: Optional[bool] = Query(None, description="是否已验证"),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    try:
        user_service = UserService(db)
        users, total = await user_service.get_users(
            skip=skip,
            limit=limit,
            search=search,
            is_active=is_active,
            is_verified=is_verified
        )
        
        # 计算分页信息
        page = (skip // limit) + 1
        pages = math.ceil(total / limit) if total > 0 else 1
        
        return UserListResponse(
            users=[UserResponse.model_validate(user) for user in users],
            total=total,
            page=page,
            size=limit,
            pages=pages
        )
    except Exception as e:
        logger.error(f"获取用户列表时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """根据ID获取用户"""
    try:
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return UserResponse.model_validate(user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户失败"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新用户"""
    try:
        user_service = UserService(db)
        user = await user_service.update_user(user_id, user_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return UserResponse.model_validate(user)
    except ValueError as e:
        logger.warning(f"更新用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """删除用户"""
    try:
        user_service = UserService(db)
        success = await user_service.delete_user(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.patch("/{user_id}/password", status_code=status.HTTP_200_OK)
async def update_password(
    user_id: int,
    password_data: UserPasswordUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新用户密码"""
    try:
        user_service = UserService(db)
        success = await user_service.update_password(
            user_id,
            password_data.current_password,
            password_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return {"message": "密码更新成功"}
    except ValueError as e:
        logger.warning(f"更新密码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新密码时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新密码失败"
        )


@router.patch("/{user_id}/activate", status_code=status.HTTP_200_OK)
async def activate_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """激活用户"""
    try:
        user_service = UserService(db)
        success = await user_service.activate_user(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return {"message": "用户激活成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"激活用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="激活用户失败"
        )


@router.patch("/{user_id}/deactivate", status_code=status.HTTP_200_OK)
async def deactivate_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """停用用户"""
    try:
        user_service = UserService(db)
        success = await user_service.deactivate_user(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return {"message": "用户停用成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停用用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停用用户失败"
        )


@router.patch("/{user_id}/verify", status_code=status.HTTP_200_OK)
async def verify_user(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """验证用户"""
    try:
        user_service = UserService(db)
        success = await user_service.verify_user(user_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 ID {user_id} 不存在"
            )
        
        return {"message": "用户验证成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证用户时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证用户失败"
        )


@router.post("/authenticate", response_model=UserResponse)
async def authenticate_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """用户认证"""
    try:
        user_service = UserService(db)
        user = await user_service.authenticate_user(
            login_data.username,
            login_data.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        return UserResponse.model_validate(user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户认证时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="用户认证失败"
        )
