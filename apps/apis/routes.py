#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : routes.py
# @Update  : 2025/8/7 09:45 创建API路由文件

from fastapi import APIRouter, Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from configs.database import get_db
from configs.redis_config import RedisService, get_redis_service
from configs.settings import settings
from configs.logging_config import get_app_logger
from contexts import get_request_context, get_request_id
from services.graph_service import GraphService
from services.vector_service import VectorService

# 获取应用日志器
logger = get_app_logger()

# 创建路由器
router = APIRouter()


@router.get("/")
async def root():
    """根路径"""
    return {
        "message":  "欢迎使用EchoNote API",
        "app_name": settings.app_name,
        "version":  settings.app_version
    }


@router.get("/health")
async def health_check(
    db: AsyncSession = Depends(get_db),
    redis_service: RedisService = Depends(get_redis_service)
):
    """健康检查接口"""
    # 获取请求上下文信息
    request_id = get_request_id()
    context = get_request_context()

    logger.info(
        "健康检查请求", extra={
            "request_id": request_id,
            "endpoint":   "/health"
        }
        )

    health_status = {
        "status":     "healthy",
        "request_id": request_id,
        "app_name":   settings.app_name,
        "version":    settings.app_version,
        "debug":      settings.debug,
        "services":   {},
        "extensions": {},
        "context":    {
            "request_duration": context.get_duration() if context else 0,
            "client_ip":        context.ip_address if context else None
        }
    }

    # 检查数据库
    try:
        await db.execute(text("SELECT 1"))
        health_status["services"]["database"] = "healthy"
    except Exception as e:
        health_status["services"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"

    # 检查pgvector扩展
    try:
        result = await db.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'vector'"))
        if result.fetchone():
            health_status["extensions"]["pgvector"] = "enabled"
        else:
            health_status["extensions"]["pgvector"] = "not_installed"
    except Exception as e:
        health_status["extensions"]["pgvector"] = f"error: {str(e)}"

    # 检查Apache AGE扩展
    try:
        result = await db.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'age'"))
        if result.fetchone():
            health_status["extensions"]["apache_age"] = "enabled"
        else:
            health_status["extensions"]["apache_age"] = "not_installed"
    except Exception as e:
        health_status["extensions"]["apache_age"] = f"error: {str(e)}"

    # 检查Redis
    try:
        await redis_service.redis.ping()
        health_status["services"]["redis"] = "healthy"
    except Exception as e:
        health_status["services"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"

    # 检查向量数据库功能
    try:
        vector_service = VectorService(db)
        stats = await vector_service.get_embedding_stats()
        health_status["services"]["vector_db"] = {
            "status": "healthy",
            "stats":  stats
        }
    except Exception as e:
        health_status["services"]["vector_db"] = f"unhealthy: {str(e)}"

    # 检查图数据库功能
    try:
        graph_service = GraphService(db)

        # 检查AGE扩展是否可用
        age_available = await graph_service._check_age_availability()

        if age_available:
            stats = await graph_service.get_graph_stats()
            health_status["services"]["graph_db"] = {
                "status": "healthy",
                "stats":  stats
            }
        else:
            health_status["services"]["graph_db"] = {
                "status":  "extension_unavailable",
                "message": "Apache AGE extension is not properly installed or configured"
            }
    except Exception as e:
        health_status["services"]["graph_db"] = f"unhealthy: {str(e)}"

    return health_status


@router.get("/hello/{name}")
async def say_hello(name: str):
    """问候接口"""
    return {"message": f"你好 {name}，欢迎使用EchoNote！"}
