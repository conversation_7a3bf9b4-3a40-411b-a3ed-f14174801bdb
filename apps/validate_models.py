#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 11:30
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : validate_models.py
# @Update  : 2025/1/27 11:30 模型验证脚本

"""
模型验证脚本

用于验证博客系统所有模型的关系配置是否正确。
"""

import sys
from typing import Dict, List, Any

from models import (
    User, Category, Tag, Post, Comment, PostView, 
    Page, Media, FileUpload, Setting, MenuItem, 
    ThemeSetting, Notification, post_tag_association
)


def validate_model_relationships() -> Dict[str, Any]:
    """验证所有模型的关系配置"""
    
    results = {
        "success": True,
        "errors": [],
        "warnings": [],
        "model_info": {}
    }
    
    # 定义预期的关系映射
    expected_relationships = {
        "User": {
            "posts": {"type": "one-to-many", "target": "Post", "back_populates": "author"},
            "comments": {"type": "one-to-many", "target": "Comment", "back_populates": "author"},
            "pages": {"type": "one-to-many", "target": "Page", "back_populates": "author"},
            "media": {"type": "one-to-many", "target": "Media", "back_populates": "uploader"},
            "file_uploads": {"type": "one-to-many", "target": "FileUpload", "back_populates": "uploader"},
            "menu_items": {"type": "one-to-many", "target": "MenuItem", "back_populates": "created_by"},
            "notifications": {"type": "one-to-many", "target": "Notification", "back_populates": "recipient"},
        },
        "Post": {
            "author": {"type": "many-to-one", "target": "User", "back_populates": "posts"},
            "category": {"type": "many-to-one", "target": "Category", "back_populates": "posts"},
            "tags": {"type": "many-to-many", "target": "Tag", "back_populates": "posts"},
            "comments": {"type": "one-to-many", "target": "Comment", "back_populates": "post"},
        },
        "Category": {
            "posts": {"type": "one-to-many", "target": "Post", "back_populates": "category"},
        },
        "Tag": {
            "posts": {"type": "many-to-many", "target": "Post", "back_populates": "tags"},
        },
        "Comment": {
            "post": {"type": "many-to-one", "target": "Post", "back_populates": "comments"},
            "author": {"type": "many-to-one", "target": "User", "back_populates": "comments"},
            "parent": {"type": "many-to-one", "target": "Comment", "back_populates": "replies"},
            "replies": {"type": "one-to-many", "target": "Comment", "back_populates": "parent"},
        },
        "Page": {
            "author": {"type": "many-to-one", "target": "User", "back_populates": "pages"},
        },
        "Media": {
            "uploader": {"type": "many-to-one", "target": "User", "back_populates": "media"},
        },
        "FileUpload": {
            "uploader": {"type": "many-to-one", "target": "User", "back_populates": "file_uploads"},
        },
        "MenuItem": {
            "created_by": {"type": "many-to-one", "target": "User", "back_populates": "menu_items"},
            "parent": {"type": "many-to-one", "target": "MenuItem", "back_populates": "children"},
            "children": {"type": "one-to-many", "target": "MenuItem", "back_populates": "parent"},
        },
        "Notification": {
            "recipient": {"type": "many-to-one", "target": "User", "back_populates": "notifications"},
            "sender": {"type": "many-to-one", "target": "User"},
        },
    }
    
    # 验证每个模型的关系
    for model_name, expected_rels in expected_relationships.items():
        model_class = globals()[model_name]
        
        model_info = {
            "table_name": model_class.__tablename__,
            "relationships": {},
            "attributes": []
        }
        
        # 检查关系
        for rel_name, expected_rel in expected_rels.items():
            if hasattr(model_class, rel_name):
                rel = getattr(model_class, rel_name)
                model_info["relationships"][rel_name] = {
                    "exists": True,
                    "type": expected_rel["type"],
                    "target": expected_rel["target"],
                    "back_populates": expected_rel.get("back_populates")
                }
            else:
                results["errors"].append(f"{model_name}: 缺少关系 {rel_name}")
                model_info["relationships"][rel_name] = {
                    "exists": False,
                    "type": expected_rel["type"],
                    "target": expected_rel["target"],
                    "back_populates": expected_rel.get("back_populates")
                }
        
        # 检查基本属性
        for attr_name in dir(model_class):
            if not attr_name.startswith('_') and not callable(getattr(model_class, attr_name)):
                model_info["attributes"].append(attr_name)
        
        results["model_info"][model_name] = model_info
    
    # 检查关联表
    results["model_info"]["post_tag_association"] = {
        "table_name": post_tag_association.name,
        "columns": [col.name for col in post_tag_association.columns],
        "relationships": {},
        "attributes": []
    }
    
    return results


def print_validation_results(results: Dict[str, Any]) -> None:
    """打印验证结果"""
    
    print("=" * 60)
    print("博客系统模型验证报告")
    print("=" * 60)
    
    if results["errors"]:
        print(f"\n❌ 发现 {len(results['errors'])} 个错误:")
        for error in results["errors"]:
            print(f"  - {error}")
        results["success"] = False
    
    if results["warnings"]:
        print(f"\n⚠️  发现 {len(results['warnings'])} 个警告:")
        for warning in results["warnings"]:
            print(f"  - {warning}")
    
    if not results["errors"] and not results["warnings"]:
        print("\n✅ 所有模型关系配置正确！")
    
    print(f"\n📊 模型统计:")
    print(f"  - 总模型数: {len(results['model_info'])}")
    
    for model_name, info in results["model_info"].items():
        print(f"\n📋 {model_name} ({info['table_name']}):")
        print(f"  - 关系数: {len(info['relationships'])}")
        print(f"  - 属性数: {len(info['attributes'])}")
        
        if info['relationships']:
            print("  - 关系详情:")
            for rel_name, rel_info in info['relationships'].items():
                status = "✅" if rel_info['exists'] else "❌"
                print(f"    {status} {rel_name}: {rel_info['type']} -> {rel_info['target']}")
                if rel_info.get('back_populates'):
                    print(f"      └─ back_populates: {rel_info['back_populates']}")
        
        if model_name == "post_tag_association" and info['columns']:
            print("  - 关联表列:")
            for col in info['columns']:
                print(f"    * {col}")
    
    print("\n" + "=" * 60)


def main():
    """主函数"""
    try:
        print("🔍 开始验证博客系统模型...")
        results = validate_model_relationships()
        print_validation_results(results)
        
        if not results["success"]:
            sys.exit(1)
        else:
            print("🎉 模型验证完成！")
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
