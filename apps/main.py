#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : main.py
# @Update  : 2025/8/7 09:45 更新FastAPI应用配置

from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from sqlalchemy import text

from apis import router
from configs.database import db_manager
from configs.logging_config import get_app_logger
from configs.redis_config import redis_manager
from configs.settings import settings
from middleware import LoggingMiddleware, RequestIDMiddleware

# 获取应用日志器
logger = get_app_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动EchoNote应用...")

    try:
        # 检查数据库连接
        engine = db_manager.create_engine()
        async with engine.begin() as conn:
            logger.info("数据库连接成功")

            # 检查pgvector扩展
            try:
                await conn.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'vector'"))
                logger.info("pgvector扩展已启用")
            except Exception as e:
                logger.warning(f"pgvector扩展检查失败: {e}")

            # 检查Apache AGE扩展
            try:
                await conn.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'age'"))
                logger.info("Apache AGE扩展已启用")
            except Exception as e:
                logger.warning(f"Apache AGE扩展检查失败: {e}")

        # 检查Redis连接
        if await redis_manager.ping():
            logger.info("Redis连接成功")
        else:
            logger.warning("Redis连接失败")

        # 数据库表管理交给 Alembic 处理
        logger.info("数据库连接验证完成，请使用 Alembic 管理数据库迁移")

        logger.info("EchoNote应用启动完成")

    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("正在关闭EchoNote应用...")

    try:
        # 关闭数据库连接
        await db_manager.close()
        logger.info("数据库连接已关闭")

        # 关闭Redis连接
        await redis_manager.close()
        logger.info("Redis连接已关闭")

        logger.info("EchoNote应用关闭完成")

    except Exception as e:
        logger.error(f"应用关闭时出错: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description=settings.app_description,
    debug=settings.debug,
    lifespan=lifespan
)

# 添加请求ID中间件（最先添加）
app.add_middleware(
    RequestIDMiddleware,
    request_id_header="X-Request-ID",
    trace_id_header="X-Trace-ID",
    correlation_id_header="X-Correlation-ID",
    generate_if_missing=True,
    log_requests=True
)

# 添加日志中间件
app.add_middleware(
    LoggingMiddleware,
    log_request_body=settings.debug,  # 只在调试模式下记录请求体
    log_response_body=False,
    max_body_size=1024,
    exclude_paths=["/health", "/metrics", "/favicon.ico", "/docs", "/openapi.json"]
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# 添加受信任主机中间件（非调试模式推荐）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境中应该设置具体的主机名
    )


# 包含API路由
app.include_router(router)
