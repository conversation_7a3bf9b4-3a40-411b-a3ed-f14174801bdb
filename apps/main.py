#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : main.py
# @Update  : 2025/8/7 09:45 更新FastAPI应用配置

from contextlib import asynccontextmanager

from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from configs.database import db_manager, get_db
from configs.logging_config import get_app_logger
from configs.redis_config import RedisService, get_redis_service, redis_manager
from configs.settings import settings
from contexts import get_request_context, get_request_id
from middleware import LoggingMiddleware, RequestIDMiddleware
from services.graph_service import GraphService
from services.vector_service import VectorService

# 获取应用日志器
logger = get_app_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动EchoNote应用...")

    try:
        # 检查数据库连接
        engine = db_manager.create_engine()
        async with engine.begin() as conn:
            logger.info("数据库连接成功")

            # 检查pgvector扩展
            try:
                await conn.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'vector'"))
                logger.info("pgvector扩展已启用")
            except Exception as e:
                logger.warning(f"pgvector扩展检查失败: {e}")

            # 检查Apache AGE扩展
            try:
                await conn.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'age'"))
                logger.info("Apache AGE扩展已启用")
            except Exception as e:
                logger.warning(f"Apache AGE扩展检查失败: {e}")

        # 检查Redis连接
        if await redis_manager.ping():
            logger.info("Redis连接成功")
        else:
            logger.warning("Redis连接失败")

        # 数据库表管理交给 Alembic 处理
        logger.info("数据库连接验证完成，请使用 Alembic 管理数据库迁移")

        logger.info("EchoNote应用启动完成")

    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("正在关闭EchoNote应用...")

    try:
        # 关闭数据库连接
        await db_manager.close()
        logger.info("数据库连接已关闭")

        # 关闭Redis连接
        await redis_manager.close()
        logger.info("Redis连接已关闭")

        logger.info("EchoNote应用关闭完成")

    except Exception as e:
        logger.error(f"应用关闭时出错: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description=settings.app_description,
    debug=settings.debug,
    lifespan=lifespan
)

# 添加请求ID中间件（最先添加）
app.add_middleware(
    RequestIDMiddleware,
    request_id_header="X-Request-ID",
    trace_id_header="X-Trace-ID",
    correlation_id_header="X-Correlation-ID",
    generate_if_missing=True,
    log_requests=True
)

# 添加日志中间件
app.add_middleware(
    LoggingMiddleware,
    log_request_body=settings.debug,  # 只在调试模式下记录请求体
    log_response_body=False,
    max_body_size=1024,
    exclude_paths=["/health", "/metrics", "/favicon.ico", "/docs", "/openapi.json"]
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# 添加受信任主机中间件（非调试模式推荐）
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境中应该设置具体的主机名
    )


@app.get("/")
async def root():
    """根路径"""
    return {
        "message":  "欢迎使用EchoNote API",
        "app_name": settings.app_name,
        "version":  settings.app_version
    }


@app.get("/health")
async def health_check(
    db: AsyncSession = Depends(get_db),
    redis_service: RedisService = Depends(get_redis_service)
):
    """健康检查接口"""
    # 获取请求上下文信息
    request_id = get_request_id()
    context = get_request_context()

    logger.info(
        "健康检查请求", extra={
            "request_id": request_id,
            "endpoint":   "/health"
        }
        )

    health_status = {
        "status":     "healthy",
        "request_id": request_id,
        "app_name":   settings.app_name,
        "version":    settings.app_version,
        "debug":      settings.debug,
        "services":   {},
        "extensions": {},
        "context":    {
            "request_duration": context.get_duration() if context else 0,
            "client_ip":        context.ip_address if context else None
        }
    }

    # 检查数据库
    try:
        await db.execute(text("SELECT 1"))
        health_status["services"]["database"] = "healthy"
    except Exception as e:
        health_status["services"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"

    # 检查pgvector扩展
    try:
        result = await db.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'vector'"))
        if result.fetchone():
            health_status["extensions"]["pgvector"] = "enabled"
        else:
            health_status["extensions"]["pgvector"] = "not_installed"
    except Exception as e:
        health_status["extensions"]["pgvector"] = f"error: {str(e)}"

    # 检查Apache AGE扩展
    try:
        result = await db.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'age'"))
        if result.fetchone():
            health_status["extensions"]["apache_age"] = "enabled"
        else:
            health_status["extensions"]["apache_age"] = "not_installed"
    except Exception as e:
        health_status["extensions"]["apache_age"] = f"error: {str(e)}"

    # 检查Redis
    try:
        await redis_service.redis.ping()
        health_status["services"]["redis"] = "healthy"
    except Exception as e:
        health_status["services"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"

    # 检查向量数据库功能
    try:
        vector_service = VectorService(db)
        stats = await vector_service.get_embedding_stats()
        health_status["services"]["vector_db"] = {
            "status": "healthy",
            "stats":  stats
        }
    except Exception as e:
        health_status["services"]["vector_db"] = f"unhealthy: {str(e)}"

    # 检查图数据库功能
    try:
        graph_service = GraphService(db)

        # 检查AGE扩展是否可用
        age_available = await graph_service._check_age_availability()

        if age_available:
            stats = await graph_service.get_graph_stats()
            health_status["services"]["graph_db"] = {
                "status": "healthy",
                "stats":  stats
            }
        else:
            health_status["services"]["graph_db"] = {
                "status":  "extension_unavailable",
                "message": "Apache AGE extension is not properly installed or configured"
            }
    except Exception as e:
        health_status["services"]["graph_db"] = f"unhealthy: {str(e)}"

    return health_status


@app.get("/hello/{name}")
async def say_hello(name: str):
    """问候接口"""
    return {"message": f"你好 {name}，欢迎使用EchoNote！"}
