#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : user_service.py
# @Update  : 2025/8/7 09:45 用户服务

import hashlib
from datetime import datetime
from typing import List, Optional, Tuple

from pydantic import EmailStr
from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from configs.logging_config import get_service_logger
from models.user import User
from schemas.user import UserCreate, UserUpdate

# 获取服务日志器
logger = get_service_logger()


class UserService:
    """用户服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """验证密码"""
        return hashlib.sha256(password.encode()).hexdigest() == hashed_password

    async def create_user(self, user_data: UserCreate) -> User:
        """创建用户"""
        logger.info(f"创建用户: {user_data.username}")
        
        # 检查用户名是否已存在
        existing_user = await self.get_user_by_username(user_data.username)
        if existing_user:
            raise ValueError(f"用户名 '{user_data.username}' 已存在")
        
        # 检查邮箱是否已存在
        existing_email = await self.get_user_by_email(user_data.email)
        if existing_email:
            raise ValueError(f"邮箱 '{user_data.email}' 已存在")

        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            avatar_url=user_data.avatar_url,
            bio=user_data.bio,
            hashed_password=self.hash_password(user_data.password),
            is_active=True,
            is_superuser=False,
            is_verified=False
        )

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info(f"用户创建成功: {user.username} (ID: {user.id})")
        return user

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await self.db.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def get_user_by_email(self, email: EmailStr) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_user_by_username_or_email(self, identifier: str) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        result = await self.db.execute(
            select(User).where(
                or_(User.username == identifier, User.email == identifier)
            )
        )
        return result.scalar_one_or_none()

    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """更新用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None

        logger.info(f"更新用户: {user.username} (ID: {user_id})")

        # 检查用户名是否已被其他用户使用
        if user_data.username and user_data.username != user.username:
            existing_user = await self.get_user_by_username(user_data.username)
            if existing_user and existing_user.id != user_id:
                raise ValueError(f"用户名 '{user_data.username}' 已存在")

        # 检查邮箱是否已被其他用户使用
        if user_data.email and user_data.email != user.email:
            existing_email = await self.get_user_by_email(user_data.email)
            if existing_email and existing_email.id != user_id:
                raise ValueError(f"邮箱 '{user_data.email}' 已存在")

        # 更新字段
        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)

        user.updated_at = datetime.utcnow()
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info(f"用户更新成功: {user.username} (ID: {user_id})")
        return user

    async def update_password(self, user_id: int, current_password: str, new_password: str) -> bool:
        """更新密码"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        # 验证当前密码
        if not self.verify_password(current_password, user.hashed_password):
            raise ValueError("当前密码不正确")

        # 更新密码
        user.hashed_password = self.hash_password(new_password)
        user.updated_at = datetime.utcnow()
        
        await self.db.commit()
        logger.info(f"用户密码更新成功: {user.username} (ID: {user_id})")
        return True

    async def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        logger.info(f"删除用户: {user.username} (ID: {user_id})")
        await self.db.delete(user)
        await self.db.commit()
        
        logger.info(f"用户删除成功: ID {user_id}")
        return True

    async def get_users(
        self,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_verified: Optional[bool] = None
    ) -> Tuple[List[User], int]:
        """获取用户列表"""
        query = select(User)
        count_query = select(func.count(User.id))

        # 添加过滤条件
        conditions = []
        
        if search:
            search_condition = or_(
                User.username.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
                User.full_name.ilike(f"%{search}%")
            )
            conditions.append(search_condition)
        
        if is_active is not None:
            conditions.append(User.is_active == is_active)
            
        if is_verified is not None:
            conditions.append(User.is_verified == is_verified)

        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))

        # 获取总数
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 获取用户列表
        query = query.order_by(User.created_at.desc()).offset(skip).limit(limit)
        result = await self.db.execute(query)
        users = result.scalars().all()

        return list(users), total

    async def authenticate_user(self, identifier: str, password: str) -> Optional[User]:
        """用户认证"""
        user = await self.get_user_by_username_or_email(identifier)
        if not user:
            return None
        
        if not self.verify_password(password, user.hashed_password):
            return None
        
        if not user.is_active:
            return None

        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        await self.db.commit()
        
        logger.info(f"用户认证成功: {user.username} (ID: {user.id})")
        return user

    async def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.is_active = True
        user.updated_at = datetime.utcnow()
        await self.db.commit()
        
        logger.info(f"用户激活成功: {user.username} (ID: {user_id})")
        return True

    async def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.is_active = False
        user.updated_at = datetime.utcnow()
        await self.db.commit()
        
        logger.info(f"用户停用成功: {user.username} (ID: {user_id})")
        return True

    async def verify_user(self, user_id: int) -> bool:
        """验证用户"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False

        user.is_verified = True
        user.updated_at = datetime.utcnow()
        await self.db.commit()
        
        logger.info(f"用户验证成功: {user.username} (ID: {user_id})")
        return True
