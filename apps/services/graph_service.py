#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 11:15
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : graph_service.py
# @Update  : 2025/8/7 11:15 图数据库服务

import json
from typing import Any, Dict, List, Optional

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from configs.logging_config import get_service_logger
from configs.settings import settings
from models.graph import GraphEdge, GraphNode

# 获取服务日志器
logger = get_service_logger()


class GraphService:
    """图数据库服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def _check_age_availability(self) -> bool:
        """检查AGE扩展是否可用"""
        try:
            # 检查扩展是否安装
            result = await self.db.execute(text("SELECT 1 FROM pg_extension WHERE extname = 'age'"))
            if not result.fetchone():
                return False

            # 检查agtype类型是否存在
            result = await self.db.execute(text("SELECT 1 FROM pg_type WHERE typname = 'agtype'"))
            if not result.fetchone():
                return False

            # 检查ag_catalog模式是否存在
            result = await self.db.execute(
                text("SELECT 1 FROM information_schema.schemata WHERE schema_name = 'ag_catalog'")
            )
            if not result.fetchone():
                return False

            return True
        except Exception as e:
            logger.error(f"AGE availability check failed: {e}", exc_info=True)
            return False

    async def _ensure_graph_exists(self) -> bool:
        """确保图数据库存在"""
        try:
            # 检查图是否已存在
            result = await self.db.execute(
                text("SELECT 1 FROM ag_catalog.ag_graph WHERE name = :graph_name"),
                {"graph_name": settings.graph_name}
            )
            if result.fetchone():
                return True

            # 尝试创建图
            await self.db.execute(
                text(f"SELECT ag_catalog.create_graph('{settings.graph_name}')")
            )
            await self.db.commit()
            return True

        except Exception as e:
            logger.warning(f"Failed to ensure graph exists: {e}")
            return False

    async def execute_cypher(
        self, cypher_query: str, parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """执行Cypher查询"""

        # 检查AGE扩展是否可用
        if not await self._check_age_availability():
            logger.warning("AGE extension is not available")
            return []

        # 确保图数据库存在
        if not await self._ensure_graph_exists():
            logger.warning("Failed to ensure graph exists")
            return []

        # 构建AGE查询
        if parameters:
            # 简单的参数替换（生产环境应该使用更安全的方法）
            for key, value in parameters.items():
                if isinstance(value, str):
                    cypher_query = cypher_query.replace(f"${key}", f"'{value}'")
                else:
                    cypher_query = cypher_query.replace(f"${key}", str(value))

        # 检查查询是否返回多个列
        if ',' in cypher_query.split('RETURN')[1] if 'RETURN' in cypher_query else False:
            # 多列查询，需要动态构建列定义
            return_part = cypher_query.split('RETURN')[1].strip()
            columns = [col.strip().split(' as ')[1] if ' as ' in col else f"col_{i}"
                       for i, col in enumerate(return_part.split(','))]
            column_defs = ', '.join([f"{col} agtype" for col in columns])
            age_query = f"""
            SELECT * FROM ag_catalog.cypher('{settings.graph_name}', $$
                {cypher_query}
            $$) AS ({column_defs});
            """
        else:
            # 单列查询
            age_query = f"""
            SELECT * FROM ag_catalog.cypher('{settings.graph_name}', $$
                {cypher_query}
            $$) AS (result agtype);
            """
        logger.info(f"Executing age query: {age_query}")
        try:
            result = await self.db.execute(text(age_query))
            rows = result.fetchall()

            # 解析AGE结果
            results = []
            for row in rows:
                if hasattr(row, 'result'):
                    # 单列结果
                    if row.result:
                        try:
                            parsed = json.loads(str(row.result))
                            results.append(parsed)
                        except (json.JSONDecodeError, AttributeError):
                            results.append({"result": str(row.result)})
                else:
                    # 多列结果
                    row_dict = {}
                    for i, col in enumerate(row):
                        try:
                            if col:
                                parsed = json.loads(str(col))
                                row_dict[list(row._fields)[i]] = parsed
                            else:
                                row_dict[list(row._fields)[i]] = None
                        except (json.JSONDecodeError, AttributeError):
                            row_dict[list(row._fields)[i]] = str(col)
                    results.append(row_dict)

            return results

        except Exception as e:
            logger.error(f"Cypher query error: {e}", exc_info=True)
            return []

    async def create_node(
        self,
        label: str,
        properties: Dict[str, Any],
        node_id: Optional[str] = None
    ) -> Optional[str]:
        """创建图节点"""

        # 生成节点ID
        if not node_id:
            node_id = f"{label}_{len(properties.get('name', 'node'))}"

        # 构建属性字符串
        props_str = ", ".join(
            [f"{k}: '{v}'" if isinstance(v, str) else f"{k}: {v}"
             for k, v in properties.items()]
        )

        # 创建节点的Cypher查询
        cypher = f"CREATE (n:{label} {{{props_str}}}) RETURN n"

        result = await self.execute_cypher(cypher)

        if result:
            # 在元数据表中记录节点信息
            node_meta = GraphNode(
                graph_name=settings.graph_name,
                node_id=node_id,
                label=label,
                name=properties.get('name'),
                properties=properties
            )

            self.db.add(node_meta)
            await self.db.commit()

            return node_id

        return None

    async def create_edge(
        self,
        source_node_id: str,
        target_node_id: str,
        label: str,
        properties: Optional[Dict[str, Any]] = None,
        weight: Optional[float] = None
    ) -> Optional[str]:
        """创建图边"""

        properties = properties or {}
        if weight is not None:
            properties['weight'] = weight

        # 构建属性字符串
        props_str = ""
        if properties:
            props_str = "{" + ", ".join(
                [f"{k}: '{v}'" if isinstance(v, str) else f"{k}: {v}"
                 for k, v in properties.items()]
            ) + "}"

        # 创建边的Cypher查询
        cypher = f"""
        MATCH (a), (b)
        WHERE id(a) = {source_node_id} AND id(b) = {target_node_id}
        CREATE (a)-[r:{label} {props_str}]->(b)
        RETURN r
        """

        result = await self.execute_cypher(cypher)

        if result:
            # 生成边ID
            edge_id = f"{source_node_id}_{label}_{target_node_id}"

            # 在元数据表中记录边信息
            edge_meta = GraphEdge(
                graph_name=settings.graph_name,
                edge_id=edge_id,
                label=label,
                source_node_id=source_node_id,
                target_node_id=target_node_id,
                properties=properties,
                weight=weight
            )

            self.db.add(edge_meta)
            await self.db.commit()

            # 更新节点度数
            await self._update_node_degrees(source_node_id, target_node_id)

            return edge_id

        return None

    async def find_nodes(
        self,
        label: Optional[str] = None,
        properties: Optional[Dict[str, Any]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """查找图节点"""

        # 构建查询条件
        where_conditions = []
        if label:
            where_conditions.append(f"n:{label}")

        if properties:
            for key, value in properties.items():
                if isinstance(value, str):
                    where_conditions.append(f"n.{key} = '{value}'")
                else:
                    where_conditions.append(f"n.{key} = {value}")

        where_clause = " AND ".join(where_conditions) if where_conditions else ""

        # 构建Cypher查询
        cypher = f"MATCH (n) {('WHERE ' + where_clause) if where_clause else ''} RETURN n LIMIT {limit}"

        return await self.execute_cypher(cypher)

    async def find_paths(
        self,
        start_node_id: str,
        end_node_id: str,
        max_depth: int = 5,
        relationship_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """查找节点间的路径"""

        # 构建关系类型过滤
        rel_filter = ""
        if relationship_types:
            rel_filter = ":" + "|".join(relationship_types)

        # 构建路径查询
        cypher = f"""
        MATCH path = (start)-[{rel_filter}*1..{max_depth}]->(end)
        WHERE id(start) = {start_node_id} AND id(end) = {end_node_id}
        RETURN path
        LIMIT 10
        """

        return await self.execute_cypher(cypher)

    async def get_node_neighbors(
        self,
        node_id: str,
        direction: str = "both",  # "in", "out", "both"
        relationship_types: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取节点的邻居"""

        # 构建关系类型过滤
        rel_filter = ""
        if relationship_types:
            rel_filter = ":" + "|".join(relationship_types)

        # 构建方向查询
        if direction == "out":
            pattern = f"(n)-[r{rel_filter}]->(neighbor)"
        elif direction == "in":
            pattern = f"(n)<-[r{rel_filter}]-(neighbor)"
        else:  # both
            pattern = f"(n)-[r{rel_filter}]-(neighbor)"

        cypher = f"""
        MATCH {pattern}
        WHERE id(n) = {node_id}
        RETURN neighbor, r
        LIMIT {limit}
        """

        return await self.execute_cypher(cypher)

    async def delete_node(self, node_id: str) -> bool:
        """删除图节点"""

        # 删除节点及其所有关系
        cypher = f"""
        MATCH (n)
        WHERE id(n) = {node_id}
        DETACH DELETE n
        """

        result = await self.execute_cypher(cypher)

        if result is not None:
            # 删除元数据
            await self.db.execute(
                text("DELETE FROM graph_nodes WHERE node_id = :node_id"),
                {"node_id": node_id}
            )
            await self.db.execute(
                text(
                    "DELETE FROM graph_edges WHERE source_node_id = :node_id OR target_node_id = :node_id"
                ),
                {"node_id": node_id}
            )
            await self.db.commit()

            return True

        return False

    async def delete_edge(self, source_node_id: str, target_node_id: str, label: str) -> bool:
        """删除图边"""

        cypher = f"""
        MATCH (a)-[r:{label}]->(b)
        WHERE id(a) = {source_node_id} AND id(b) = {target_node_id}
        DELETE r
        """

        result = await self.execute_cypher(cypher)

        if result is not None:
            # 删除元数据
            edge_id = f"{source_node_id}_{label}_{target_node_id}"
            await self.db.execute(
                text("DELETE FROM graph_edges WHERE edge_id = :edge_id"),
                {"edge_id": edge_id}
            )
            await self.db.commit()

            # 更新节点度数
            await self._update_node_degrees(source_node_id, target_node_id, increment=False)

            return True

        return False

    async def get_graph_stats(self) -> Dict[str, Any]:
        """获取图数据库统计信息"""

        # 检查AGE扩展是否可用
        if not await self._check_age_availability():
            return {
                "graph_name":         settings.graph_name,
                "node_count":         0,
                "edge_count":         0,
                "label_distribution": [],
                "avg_degree":         0,
                "status":             "age_unavailable",
                "message":            "Apache AGE extension is not properly configured"
            }

        try:
            # 节点统计
            node_count_query = "MATCH (n) RETURN count(n) as count"
            node_result = await self.execute_cypher(node_count_query)
            node_count = node_result[0] if node_result else 0

            # 边统计
            edge_count_query = "MATCH ()-[r]->() RETURN count(r) as count"
            edge_result = await self.execute_cypher(edge_count_query)
            edge_count = edge_result[0] if edge_result else 0

            # 标签统计
            label_stats_query = "MATCH (n) RETURN labels(n)[0] as label, count(n) as count"
            label_result = await self.execute_cypher(label_stats_query)

            # 处理标签分布
            label_distribution = []
            if label_result:
                for item in label_result:
                    if isinstance(item, dict) and 'label' in item and 'count' in item:
                        label_distribution.append(
                            {
                                "label": item['label'],
                                "count": item['count']
                            }
                        )

            return {
                "graph_name":         settings.graph_name,
                "node_count":         node_count,
                "edge_count":         edge_count,
                "label_distribution": label_distribution,
                "avg_degree":         (edge_count * 2 / node_count) if node_count > 0 else 0,
                "status":             "healthy"
            }
        except Exception as e:
            logger.error(f"Failed to get graph stats: {e}")
            return {
                "graph_name":         settings.graph_name,
                "node_count":         0,
                "edge_count":         0,
                "label_distribution": [],
                "avg_degree":         0,
                "status":             "error",
                "message":            str(e)
            }

    async def _update_node_degrees(
        self, source_node_id: str, target_node_id: str, increment: bool = True
    ):
        """更新节点度数"""
        delta = 1 if increment else -1

        # 更新出度
        await self.db.execute(
            text(
                "UPDATE graph_nodes SET out_degree = out_degree + :delta WHERE node_id = :node_id"
            ),
            {"delta": delta, "node_id": source_node_id}
        )

        # 更新入度
        await self.db.execute(
            text("UPDATE graph_nodes SET in_degree = in_degree + :delta WHERE node_id = :node_id"),
            {"delta": delta, "node_id": target_node_id}
        )

        await self.db.commit()
