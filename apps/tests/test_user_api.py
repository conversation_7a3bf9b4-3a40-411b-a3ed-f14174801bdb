#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : test_user_api.py
# @Update  : 2025/8/7 09:45 用户API测试

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from configs.database import Base, get_db
from main import app

# 创建测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


@pytest.fixture(autouse=True)
def setup_database():
    """设置测试数据库"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def test_create_user():
    """测试创建用户"""
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPassword123",
        "full_name": "Test User"
    }
    
    response = client.post("/api/v1/users/", json=user_data)
    assert response.status_code == 201
    
    data = response.json()
    assert data["username"] == "testuser"
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "Test User"
    assert data["is_active"] is True
    assert data["is_verified"] is False


def test_get_users():
    """测试获取用户列表"""
    # 先创建一个用户
    user_data = {
        "username": "testuser2",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    client.post("/api/v1/users/", json=user_data)
    
    # 获取用户列表
    response = client.get("/api/v1/users/")
    assert response.status_code == 200
    
    data = response.json()
    assert "users" in data
    assert "total" in data
    assert data["total"] >= 1


def test_get_user_by_id():
    """测试根据ID获取用户"""
    # 先创建一个用户
    user_data = {
        "username": "testuser3",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    create_response = client.post("/api/v1/users/", json=user_data)
    user_id = create_response.json()["id"]
    
    # 获取用户
    response = client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert data["id"] == user_id
    assert data["username"] == "testuser3"


def test_update_user():
    """测试更新用户"""
    # 先创建一个用户
    user_data = {
        "username": "testuser4",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    create_response = client.post("/api/v1/users/", json=user_data)
    user_id = create_response.json()["id"]
    
    # 更新用户
    update_data = {
        "full_name": "Updated Name",
        "bio": "Updated bio"
    }
    response = client.put(f"/api/v1/users/{user_id}", json=update_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["full_name"] == "Updated Name"
    assert data["bio"] == "Updated bio"


def test_delete_user():
    """测试删除用户"""
    # 先创建一个用户
    user_data = {
        "username": "testuser5",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    create_response = client.post("/api/v1/users/", json=user_data)
    user_id = create_response.json()["id"]
    
    # 删除用户
    response = client.delete(f"/api/v1/users/{user_id}")
    assert response.status_code == 204
    
    # 验证用户已被删除
    get_response = client.get(f"/api/v1/users/{user_id}")
    assert get_response.status_code == 404


def test_authenticate_user():
    """测试用户认证"""
    # 先创建一个用户
    user_data = {
        "username": "testuser6",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    client.post("/api/v1/users/", json=user_data)
    
    # 认证用户
    login_data = {
        "username": "testuser6",
        "password": "TestPassword123"
    }
    response = client.post("/api/v1/users/authenticate", json=login_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["username"] == "testuser6"
    assert data["last_login_at"] is not None


def test_create_user_duplicate_username():
    """测试创建重复用户名的用户"""
    user_data = {
        "username": "duplicate",
        "email": "<EMAIL>",
        "password": "TestPassword123"
    }
    
    # 第一次创建应该成功
    response1 = client.post("/api/v1/users/", json=user_data)
    assert response1.status_code == 201
    
    # 第二次创建应该失败
    user_data["email"] = "<EMAIL>"  # 不同的邮箱
    response2 = client.post("/api/v1/users/", json=user_data)
    assert response2.status_code == 400


def test_create_user_invalid_password():
    """测试创建用户时密码验证"""
    user_data = {
        "username": "testuser9",
        "email": "<EMAIL>",
        "password": "weak",  # 弱密码
    }
    
    response = client.post("/api/v1/users/", json=user_data)
    assert response.status_code == 422  # 验证错误
