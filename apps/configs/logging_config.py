#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 12:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : logging_config.py
# @Update  : 2025/8/7 12:00 统一日志配置模块

import logging
import logging.config
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional


class SafeLogFormatter(logging.Formatter):
    """安全的日志格式化器，处理缺失的字段"""

    def format(self, record):
        # 确保所有必需的字段都存在
        if not hasattr(record, 'request_id'):
            record.request_id = 'no-request'
        if not hasattr(record, 'user_id'):
            record.user_id = 'system'
        if not hasattr(record, 'ip_address'):
            record.ip_address = 'system'
        if not hasattr(record, 'method'):
            record.method = ''
        if not hasattr(record, 'path'):
            record.path = ''

        return super().format(record)


class EnhancedLogger(logging.Logger):
    """增强的日志器，自动为 error 方法添加 exc_info=True"""

    def error(self, msg, *args, **kwargs):
        """重写 error 方法，自动添加 exc_info=True"""
        # 如果没有明确指定 exc_info，则设置为 True
        if 'exc_info' not in kwargs:
            kwargs['exc_info'] = True
        return super().error(msg, *args, **kwargs)

    def exception(self, msg, *args, **kwargs):
        """重写 exception 方法，确保 exc_info=True"""
        kwargs['exc_info'] = True
        return super().exception(msg, *args, **kwargs)


class LoggerManager:
    """日志管理器"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._loggers = {}
            self._log_dir = None
            self._config = None
            LoggerManager._initialized = True

    def setup_logging(self, config: Dict[str, Any], log_dir: Optional[str] = None):
        """设置日志配置"""
        self._config = config
        self._log_dir = Path(log_dir) if log_dir else Path("logs")

        # 确保日志目录存在
        self._log_dir.mkdir(exist_ok=True)

        # 注册自定义日志器类
        logging.setLoggerClass(EnhancedLogger)

        # 构建日志配置
        logging_config = self._build_logging_config()

        # 应用配置
        logging.config.dictConfig(logging_config)

        # 设置根日志级别
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.get('level', 'INFO').upper()))

    def _build_logging_config(self) -> Dict[str, Any]:
        """构建日志配置字典"""
        log_format = self._config.get(
            'format',
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        log_level = self._config.get('level', 'INFO').upper()

        # 日志文件路径
        app_log_file = self._log_dir / "app.log"
        error_log_file = self._log_dir / "error.log"
        access_log_file = self._log_dir / "access.log"

        config = {
            'version':                  1,
            'disable_existing_loggers': False,
            'formatters':               {
                'standard':      {
                    '()':      SafeLogFormatter,
                    'format':  log_format,
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
                'detailed':      {
                    '()':      SafeLogFormatter,
                    'format':  '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
                'simple':        {
                    '()':     SafeLogFormatter,
                    'format': '%(levelname)s - %(message)s'
                },
                'json':          {
                    '()':      SafeLogFormatter,
                    'format':  '{"timestamp": "%(asctime)s", "name": "%(name)s", "level": "%(levelname)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "message": "%(message)s"}',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                },
                'request_aware': {
                    '()':      SafeLogFormatter,
                    'format':  '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] [%(user_id)s] [%(ip_address)s] %(method)s %(path)s - %(message)s',
                    'datefmt': '%Y-%m-%d %H:%M:%S'
                }
            },
            'handlers':                 {
                'console':     {
                    'class':     'logging.StreamHandler',
                    'level':     log_level,
                    'formatter': 'request_aware',
                    'stream':    sys.stdout
                },
                'file':        {
                    'class':       'logging.handlers.RotatingFileHandler',
                    'level':       log_level,
                    'formatter':   'request_aware',
                    'filename':    str(app_log_file),
                    'maxBytes':    10485760,  # 10MB
                    'backupCount': 5,
                    'encoding':    'utf-8'
                },
                'error_file':  {
                    'class':       'logging.handlers.RotatingFileHandler',
                    'level':       'ERROR',
                    'formatter':   'detailed',
                    'filename':    str(error_log_file),
                    'maxBytes':    10485760,  # 10MB
                    'backupCount': 5,
                    'encoding':    'utf-8'
                },
                'access_file': {
                    'class':       'logging.handlers.RotatingFileHandler',
                    'level':       'INFO',
                    'formatter':   'standard',
                    'filename':    str(access_log_file),
                    'maxBytes':    10485760,  # 10MB
                    'backupCount': 5,
                    'encoding':    'utf-8'
                }
            },
            'loggers':                  {
                # 应用日志
                'echonote':          {
                    'level':     log_level,
                    'handlers':  ['console', 'file', 'error_file'],
                    'propagate': False
                },
                # 数据库日志
                'sqlalchemy.engine': {
                    'level':     'WARNING',
                    'handlers':  ['file'],
                    'propagate': False
                },
                'sqlalchemy.pool':   {
                    'level':     'WARNING',
                    'handlers':  ['file'],
                    'propagate': False
                },
                # Redis日志
                'redis':             {
                    'level':     'WARNING',
                    'handlers':  ['file'],
                    'propagate': False
                },
                # FastAPI访问日志
                'uvicorn.access':    {
                    'level':     'INFO',
                    'handlers':  ['access_file'],
                    'propagate': False
                },
                # FastAPI应用日志
                'uvicorn.error':     {
                    'level':     'INFO',
                    'handlers':  ['console', 'file'],
                    'propagate': False
                },
                # Alembic日志
                'alembic':           {
                    'level':     'INFO',
                    'handlers':  ['console', 'file'],
                    'propagate': False
                }
            },
            'root':                     {
                'level':    log_level,
                'handlers': ['console', 'file']
            }
        }

        return config

    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self._loggers:
            # 如果是应用相关的日志，使用echonote前缀
            if not name.startswith(('sqlalchemy', 'uvicorn', 'alembic', 'redis')):
                logger_name = f"echonote.{name}"
            else:
                logger_name = name

            self._loggers[name] = logging.getLogger(logger_name)

        return self._loggers[name]

    def set_level(self, level: str):
        """动态设置日志级别"""
        log_level = getattr(logging, level.upper())

        # 更新根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)

        # 更新应用日志器
        app_logger = logging.getLogger('echonote')
        app_logger.setLevel(log_level)

        # 更新所有处理器
        for handler in root_logger.handlers:
            if handler.name in ['console', 'file']:
                handler.setLevel(log_level)

    def add_file_handler(
        self, name: str, filename: str, level: str = 'INFO',
        formatter: str = 'standard'
        ):
        """添加文件处理器"""
        logger = self.get_logger(name)

        file_handler = logging.handlers.RotatingFileHandler(
            filename=filename,
            maxBytes=10485760,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))

        # 获取格式化器
        if hasattr(logging.getLogger().handlers[0], 'formatter'):
            file_handler.setFormatter(logging.getLogger().handlers[0].formatter)

        logger.addHandler(file_handler)
        return file_handler

    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'log_directory':  str(self._log_dir),
            'loggers_count':  len(self._loggers),
            'active_loggers': list(self._loggers.keys()),
            'log_files':      []
        }

        if self._log_dir and self._log_dir.exists():
            for log_file in self._log_dir.glob('*.log'):
                file_stats = log_file.stat()
                stats['log_files'].append(
                    {
                        'name':     log_file.name,
                        'size':     file_stats.st_size,
                        'modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    }
                )

        return stats


# 全局日志管理器实例
logger_manager = LoggerManager()


def setup_logging(config: Dict[str, Any], log_dir: Optional[str] = None):
    """设置全局日志配置"""
    logger_manager.setup_logging(config, log_dir)


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name)


# 预定义的日志器
def get_app_logger() -> logging.Logger:
    """获取应用主日志器"""
    return get_logger('app')


def get_db_logger() -> logging.Logger:
    """获取数据库日志器"""
    return get_logger('database')


def get_redis_logger() -> logging.Logger:
    """获取Redis日志器"""
    return get_logger('redis')


def get_api_logger() -> logging.Logger:
    """获取API日志器"""
    return get_logger('api')


def get_service_logger() -> logging.Logger:
    """获取服务层日志器"""
    return get_logger('service')


def get_task_logger() -> logging.Logger:
    """获取任务日志器"""
    return get_logger('task')
