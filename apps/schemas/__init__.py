#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/7 09:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : __init__.py
# @Update  : 2025/8/7 09:45 Schemas模块初始化

"""
Schemas模块

包含所有Pydantic模型定义，用于API数据验证和序列化
"""

from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserListResponse,
    UserLogin,
    UserPasswordUpdate
)

__all__ = [
    # 用户相关schemas
    "UserBase",
    "UserCreate", 
    "UserUpdate",
    "UserResponse",
    "UserListResponse",
    "UserLogin",
    "UserPasswordUpdate",
]
